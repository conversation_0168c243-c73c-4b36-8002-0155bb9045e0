import { onUnmounted } from 'vue';
import { defHttp } from '@/utils/http/axios';
import type { RequestOptions as BaseRequestOptions } from '#/axios';
import type { AxiosRequestConfig } from 'axios';

/**
 * 请求选项接口
 */
export type RequestOptions = BaseRequestOptions;

/**
 * API请求配置
 * 扩展AxiosRequestConfig，增加API特定的配置项
 */
export interface ApiRequestConfig extends AxiosRequestConfig {
  /**
   * 请求数据
   */
  data?: any;
  
  /**
   * 请求参数（会被转换为查询字符串）
   */
  params?: any;
  
  /**
   * 请求头
   */
  headers?: Record<string, string>;
  
  /**
   * 是否使用完整路径
   * 如果为true，则不添加API前缀
   */
  isFullPath?: boolean;
  
  /**
   * 请求选项
   */
  requestOptions?: RequestOptions;
}

/**
 * API响应接口
 * 定义API响应的基本结构，提供统一的数据交互格式
 */
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

/**
 * 分页查询参数接口
 * 定义标准参数，支持动态扩展
 */
export interface PageParams {
  [key: string]: any;
}

/**
 * 分页响应结果接口
 * 定义分页查询返回的标准结构，与BasicTable组件完全对应
 */
export interface PageResult<T = any> {
  /**
   * 数据列表
   * 当前页的数据项数组
   */
  list: T[];
  
  /** 分页信息 */
  pagination: any;
}

/**
 * HTTP请求方法类型
 * 定义支持的HTTP请求方法
 */
export type HttpMethod = 'get' | 'post' | 'put' | 'delete';

/**
 * API路径常量
 * 定义标准API路径后缀
 */
export const ApiPaths = {
  GET_LIST: '/getList',
  LIST_ALL: '/listAll',
  DETAIL: '',
  SAVE: '/save',
  EDIT: '/edit',
  REMOVE: '/remove',
  BATCH_SAVE: '/batchSave',
  BATCH_REMOVE: '/batchRemove',
  IMPORT_PREVIEW: '/importPreview',
  IMPORT_DATA: '/importBigData',
  EXPORT: '/export'
};

/**
 * 处理URL
 * 
 * 根据配置处理API请求的URL，支持完整路径和相对路径
 * 
 * @param {string} url - 请求URL
 * @param {string} prefix - API前缀
 * @param {boolean} isFullPath - 是否使用完整路径模式
 * @returns {string} 处理后的URL
 * 
 * @example
 * // 处理相对路径
 * const url = processUrl('/users', '/api/v1', false);
 * // 结果: '/api/v1/users'
 * 
 * // 处理完整路径
 * const url = processUrl('https://example.com/data', '/api/v1', false);
 * // 结果: 'https://example.com/data'
 * 
 * // 使用isFullPath标志
 * const url = processUrl('/users', '/api/v1', true);
 * // 结果: '/users'
 */
function processUrl(url: string, prefix: string, isFullPath: boolean): string {
  // 检查是否是完整URL（包含http(s)://）
  if (isFullPath || /^https?:\/\//.test(url)) return url;
  
  const formattedUrl = url.startsWith('/') ? url : `/${url}`;
  const formattedPrefix = prefix.endsWith('/') ? prefix.slice(0, -1) : prefix;
  
  return `${formattedPrefix}${formattedUrl}`;
}

/**
 * API操作Hook
 * 
 * 提供基于Vue组合式API的接口调用能力，将API操作封装为易用的Hooks形式
 * 支持多种HTTP方法、自动URL处理、请求取消等功能
 * 
 * @param {string} prefix - API前缀路径，会自动添加到所有相对URL前
 * @param {boolean} isFullPath - 是否使用完整路径模式，默认为false
 * @param {Record<string, string>} defaultHeaders - 默认请求头，会添加到所有请求中
 * @returns {Object} API操作方法集合，包含各种CRUD操作和工具方法
 * 
 * @example
 * // 基本用法
 * const api = useBaseApi('/api/v1');
 * 
 * // 使用完整路径模式
 * const externalApi = useBaseApi('', true);
 * 
 * // 设置默认请求头
 * const authApi = useBaseApi('/api/v1', false, { 
 *   'Authorization': 'Bearer token123' 
 * });
 * 
 * // 使用API方法
 * const users = await api.getList({ params: { pageSize: 10 } });
 * const user = await api.getDetail(123);
 * const result = await api.save({ name: '张三', age: 30 });
 */
export function useBaseApi(
  prefix: string,
  isFullPath: boolean = false,
  defaultHeaders: Record<string, string> = {}
) {
  // 用于存储待取消的请求
  const pendingRequests = new Map<string, () => void>();
  
  /**
   * 取消所有未完成的请求
   * 
   * 用于取消当前实例中所有未完成的HTTP请求，防止内存泄漏和重复响应
   * 在组件卸载时会自动调用，也可以手动调用以取消正在进行的请求
   * 
   * @example
   * // 创建API实例
   * const api = useBaseApi('/api/v1');
   * 
   * // 发起请求
   * const promise = api.getList();
   * 
   * // 取消所有未完成的请求
   * api.cancelAllRequests();
   * 
   * // 组件卸载时会自动取消请求，无需手动调用
   */
  const cancelAllRequests = () => {
    pendingRequests.forEach(cancel => cancel());
    pendingRequests.clear();
  };
  
  // 组件卸载时取消所有未完成的请求
  onUnmounted(cancelAllRequests);
  
  /**
   * 发送HTTP请求的通用方法
   * 
   * 提供统一的HTTP请求接口，支持所有HTTP方法，自动处理URL前缀和请求配置
   * 
   * @param {HttpMethod} method - HTTP请求方法(get/post/put/delete)
   * @param {string} url - 请求URL，会根据prefix和isFullPath自动处理
   * @param {ApiRequestConfig} config - 请求配置，包含data、params、headers等
   * @returns {Promise<ApiResponse<T>>} 请求响应，包含code、data、message
   * 
   * @example
   * // 基本GET请求
   * const response = await sendRequest('get', '/users');
   * 
   * // 带参数的POST请求
   * const response = await sendRequest('post', '/users', {
   *   data: { name: '张三', age: 25 },
   *   headers: { 'Content-Type': 'application/json' }
   * });
   * 
   * // 完整路径请求
   * const response = await sendRequest('get', 'https://api.example.com/data', { 
   *   isFullPath: true 
   * });
   */
  const sendRequest = async <T = any>(
    method: HttpMethod,
    url: string,
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T>> => {
    // 处理URL
    const shouldUseFullPath = config?.isFullPath || isFullPath;
    const processedUrl = processUrl(url, prefix, shouldUseFullPath);
    
    // 提取请求选项
    const { requestOptions, ...axiosConfig } = config || {};
    
    // 创建请求配置
    const requestConfig: AxiosRequestConfig = {
      url: processedUrl,
      method,
      ...axiosConfig,
      headers: { ...defaultHeaders, ...(axiosConfig?.headers || {}) }
    };
    
    // 发送请求
    return await defHttp.request<ApiResponse<T>>(requestConfig, requestOptions);
  };
  
  /**
   * 获取列表（分页）
   * 
   * 用于获取分页数据列表，自动处理分页参数和响应格式
   * 
   * @param {ApiRequestConfig} config - 请求配置，通常包含分页参数和查询条件
   * @returns {Promise<ApiResponse<PageResult<T>>>} 返回分页数据，包含列表和分页信息
   * 
   * @example
   * // 基本分页查询
   * const response = await getList({
   *   params: { 
   *     pageNum: 1,
   *     pageSize: 10,
   *     keyword: '搜索关键词'
   *   }
   * });
   * 
   * // 处理返回结果
   * if (response.code === 0) {
   *   const { list, pagination } = response.data;
   *   // 使用list数据和pagination分页信息
   * }
   */
  const getList = async <T = any>(
    config?: ApiRequestConfig
  ): Promise<ApiResponse<PageResult<T>>> => {
    return sendRequest<PageResult<T>>('get', ApiPaths.GET_LIST, config);
  };
  
  /**
   * 获取列表（不分页）
   * 
   * 用于获取完整数据列表（不分页），适用于数据量较小的场景
   * 
   * @param {ApiRequestConfig} config - 请求配置，通常包含查询条件
   * @returns {Promise<ApiResponse<T[]>>} 返回完整列表数据数组
   * 
   * @example
   * // 基本用法
   * const response = await getListAll();
   * 
   * // 带查询条件
   * const response = await getListAll({
   *   params: { 
   *     status: 'active',
   *     type: 'user'
   *   }
   * });
   * 
   * // 处理返回结果
   * if (response.code === 0) {
   *   const dataList = response.data;
   *   // 使用数据列表
   *   dataList.forEach(item => {
   *     console.log(item.name);
   *   });
   * }
   */
  const getListAll = async <T = any>(
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T[]>> => {
    return sendRequest<T[]>('get', ApiPaths.LIST_ALL, config);
  };
  
  /**
   * 查询详情
   * 
   * 用于获取单条记录的详细信息，支持多种参数形式
   * 
   * @param {string|number|ApiRequestConfig} idOrConfig - 记录ID或请求配置
   * @returns {Promise<ApiResponse<T>>} 返回详情数据
   * 
   * @example
   * // 方式1: 直接传入ID
   * const response = await getDetail(123);
   * 
   * // 方式2: 传入字符串ID
   * const response = await getDetail('user-123');
   * 
   * // 方式3: 使用配置对象，在params中指定id
   * const response = await getDetail({
   *   params: { id: 123, includeDeleted: true }
   * });
   * 
   * // 方式4: 使用配置对象，在data中指定id
   * const response = await getDetail({
   *   data: { id: 123, version: 2 }
   * });
   * 
   * // 方式5: 不传参数，获取默认详情
   * const response = await getDetail();
   * 
   * // 处理返回结果
   * if (response.code === 0) {
   *   const detail = response.data;
   *   // 使用详情数据
   *   console.log(detail.name, detail.createdTime);
   * }
   */
  const getDetail = async <T = any>(
    idOrConfig?: string | number | ApiRequestConfig
  ): Promise<ApiResponse<T>> => {
    // 处理不同类型的参数
    if (idOrConfig === undefined || idOrConfig === null) {
      return sendRequest<T>('get', ApiPaths.DETAIL);
    }
    
    if (typeof idOrConfig === 'string' || typeof idOrConfig === 'number') {
      return sendRequest<T>('get', `${ApiPaths.DETAIL}/${idOrConfig}`);
    }
    
    // 如果是配置对象，检查是否包含id
    const config = idOrConfig as ApiRequestConfig;
    const id = config.params?.id || config.data?.id;
    
    if (id !== undefined) {
      const { params, data, ...restConfig } = config;
      const newParams = { ...(params || {}), id: undefined };
      const newData = { ...(data || {}), id: undefined };
      
      return sendRequest<T>('get', `${ApiPaths.DETAIL}/${id}`, {
        ...restConfig,
        params: Object.keys(newParams).length > 1 ? newParams : undefined,
        data: Object.keys(newData).length > 1 ? newData : undefined
      });
    }
    
    return sendRequest<T>('get', ApiPaths.DETAIL, config);
  };
  
  /**
   * 编辑数据
   * 
   * 用于更新已有记录，必须提供记录ID，支持多种参数形式
   * 
   * @param {string|number|ApiRequestConfig} idOrConfig - 记录ID或请求配置
   * @returns {Promise<ApiResponse<T>>} 返回编辑结果，通常包含更新后的数据
   * 
   * @example
   * // 方式1: 直接传入ID
   * const response = await edit(123);
   * 
   * // 方式2: 使用配置对象，在params中指定id
   * const response = await edit({
   *   params: { id: 123 },
   *   data: { name: '新名称', status: 'active' }
   * });
   * 
   * // 方式3: 使用配置对象，在data中指定id
   * const response = await edit({
   *   data: { 
   *     id: 123,
   *     name: '新名称', 
   *     status: 'active' 
   *   }
   * });
   * 
   * // 处理响应
   * if (response.code === 0) {
   *   const updatedData = response.data;
   *   // 使用更新后的数据
   * } else {
   *   // 处理错误
   *   console.error(response.message);
   * }
   */
  const edit = async <T = any>(
    idOrConfig: string | number | ApiRequestConfig
  ): Promise<ApiResponse<T>> => {
    if (typeof idOrConfig === 'string' || typeof idOrConfig === 'number') {
      return sendRequest<T>('put', `${ApiPaths.EDIT}/${idOrConfig}`);
    }
    
    const config = idOrConfig as ApiRequestConfig;
    const id = config.params?.id || config.data?.id;
    
    if (id === undefined) {
      throw new Error('编辑操作需要提供ID');
    }
    
    const { params, data, ...restConfig } = config;
    const newParams = { ...(params || {}), id: undefined };
    const newData = { ...(data || {}), id: undefined };
    
    return sendRequest<T>('put', `${ApiPaths.EDIT}/${id}`, {
      ...restConfig,
      params: Object.keys(newParams).length > 1 ? newParams : undefined,
      data: Object.keys(newData).length > 1 ? newData : undefined
    });
  };
  
  /**
   * 保存数据
   * 
   * 用于创建新记录，支持直接传入数据对象或完整请求配置
   * 
   * @param {ApiRequestConfig|any} configOrData - 请求配置或要保存的数据对象
   * @returns {Promise<ApiResponse<T>>} 返回保存结果，通常包含新创建记录的ID或完整数据
   * 
   * @example
   * // 方式1: 直接传入数据对象
   * const newUser = { name: '李四', age: 30, department: '技术部' };
   * const response = await save(newUser);
   * 
   * // 方式2: 使用完整请求配置
   * const response = await save({
   *   data: { name: '李四', age: 30 },
   *   headers: { 'Custom-Header': 'value' }
   * });
   * 
   * // 方式3: 自定义URL
   * const response = await save({
   *   url: '/users/special',  // 覆盖默认URL
   *   data: { name: '李四', age: 30 }
   * });
   * 
   * // 处理响应
   * if (response.code === 0) {
   *   const savedId = response.data.id;
   *   // 使用保存后返回的数据
   * }
   */
  const save = async <T = any>(
    configOrData?: ApiRequestConfig | any
  ): Promise<ApiResponse<T>> => {
    // 如果参数不是ApiRequestConfig类型，则将其作为data处理
    if (configOrData && typeof configOrData === 'object' && !('data' in configOrData) && !('params' in configOrData)) {
      return sendRequest<T>('post', ApiPaths.SAVE, { data: configOrData });
    }
    
    return sendRequest<T>('post', ApiPaths.SAVE, configOrData as ApiRequestConfig);
  };
  
  /**
   * 批量保存数据
   * 
   * 用于批量创建或更新多条记录，支持直接传入数据数组或完整请求配置
   * 
   * @param {ApiRequestConfig|any[]} configOrData - 请求配置或要批量保存的数据数组
   * @returns {Promise<ApiResponse<T[]>>} 返回批量保存结果，通常包含所有保存后的记录
   * 
   * @example
   * // 方式1: 直接传入数据数组
   * const users = [
   *   { name: '张三', age: 25 },
   *   { name: '李四', age: 30 }
   * ];
   * const response = await batchSave(users);
   * 
   * // 方式2: 使用完整请求配置
   * const response = await batchSave({
   *   data: [
   *     { name: '张三', age: 25 },
   *     { name: '李四', age: 30 }
   *   ],
   *   headers: { 'Custom-Header': 'value' }
   * });
   * 
   * // 处理响应
   * if (response.code === 0) {
   *   const savedItems = response.data;
   *   // 使用保存后的数据列表
   *   savedItems.forEach(item => {
   *     console.log(item.id, item.name);
   *   });
   * }
   */
  const batchSave = async <T = any>(
    configOrData?: ApiRequestConfig | any[]
  ): Promise<ApiResponse<T[]>> => {
    // 如果参数是数组，则将其作为data处理
    if (Array.isArray(configOrData)) {
      return sendRequest<T[]>('post', ApiPaths.BATCH_SAVE, { data: configOrData });
    }
    
    return sendRequest<T[]>('post', ApiPaths.BATCH_SAVE, configOrData as ApiRequestConfig);
  };
  
  /**
   * 删除数据
   * 
   * 用于删除单条记录，必须提供记录ID，支持多种参数形式
   * 
   * @param {string|number|ApiRequestConfig} idOrConfig - 记录ID或请求配置
   * @returns {Promise<ApiResponse<boolean>>} 返回删除结果，通常为成功/失败标志
   * 
   * @example
   * // 方式1: 直接传入ID
   * const response = await remove(123);
   * 
   * // 方式2: 使用配置对象，在params中指定id
   * const response = await remove({
   *   params: { id: 123, permanent: true }
   * });
   * 
   * // 方式3: 使用配置对象，在data中指定id
   * const response = await remove({
   *   data: { id: 123, reason: '数据过期' }
   * });
   * 
   * // 处理响应
   * if (response.code === 0 && response.data === true) {
   *   // 删除成功
   *   console.log('记录已成功删除');
   * } else {
   *   // 删除失败
   *   console.error('删除失败:', response.message);
   * }
   */
  const remove = async (
    idOrConfig: string | number | ApiRequestConfig
  ): Promise<ApiResponse<boolean>> => {
    if (typeof idOrConfig === 'string' || typeof idOrConfig === 'number') {
      return sendRequest<boolean>('delete', `${ApiPaths.REMOVE}/${idOrConfig}`);
    }
    
    const config = idOrConfig as ApiRequestConfig;
    const id = config.params?.id || config.data?.id;
    
    if (id === undefined) {
      throw new Error('删除操作需要提供ID');
    }
    
    const { params, data, ...restConfig } = config;
    const newParams = { ...(params || {}), id: undefined };
    const newData = { ...(data || {}), id: undefined };
    
    return sendRequest<boolean>('delete', `${ApiPaths.REMOVE}/${id}`, {
      ...restConfig,
      params: Object.keys(newParams).length > 1 ? newParams : undefined,
      data: Object.keys(newData).length > 1 ? newData : undefined
    });
  };
  
  /**
   * 批量删除数据
   * 
   * 用于批量删除多条记录，支持多种参数形式
   * 
   * @param {ApiRequestConfig|(string|number)[]|{ids: (string|number)[]}} configOrIds - 请求配置、ID数组或包含ids的对象
   * @returns {Promise<ApiResponse<boolean>>} 返回批量删除结果，通常为成功/失败标志
   * 
   * @example
   * // 方式1: 直接传入ID数组
   * const response = await batchRemove([1, 2, 3]);
   * 
   * // 方式2: 传入包含ids的对象
   * const response = await batchRemove({
   *   ids: [1, 2, 3],
   *   permanent: true
   * });
   * 
   * // 方式3: 使用完整请求配置
   * const response = await batchRemove({
   *   data: { ids: [1, 2, 3], reason: '数据清理' },
   *   headers: { 'Custom-Header': 'value' }
   * });
   * 
   * // 处理响应
   * if (response.code === 0 && response.data === true) {
   *   // 批量删除成功
   *   console.log('记录已成功批量删除');
   * } else {
   *   // 批量删除失败
   *   console.error('批量删除失败:', response.message);
   * }
   */
  const batchRemove = async (
    configOrIds: ApiRequestConfig | (string | number)[] | {ids: (string | number)[], [key: string]: any}
  ): Promise<ApiResponse<boolean>> => {
    // 处理不同类型的参数
    if (Array.isArray(configOrIds)) {
      return sendRequest<boolean>('delete', ApiPaths.BATCH_REMOVE, { data: { ids: configOrIds } });
    }
    
    // 如果是普通对象但不是ApiRequestConfig
    if (configOrIds && typeof configOrIds === 'object' && 'ids' in configOrIds && !('data' in configOrIds) && !('params' in configOrIds)) {
      return sendRequest<boolean>('delete', ApiPaths.BATCH_REMOVE, { data: configOrIds });
    }
    
    return sendRequest<boolean>('delete', ApiPaths.BATCH_REMOVE, configOrIds as ApiRequestConfig);
  };
  
  /**
   * 导入预览（限制1000条）
   * 
   * 用于在正式导入前预览导入数据，通常用于验证数据格式和内容
   * 
   * @param {ApiRequestConfig} config - 请求配置，通常包含文件或数据源信息
   * @returns {Promise<ApiResponse<T[]>>} 返回预览数据，包含可能的验证结果
   * 
   
   */
  const importPreview = async <T = any>(
    config?: ApiRequestConfig
  ): Promise<ApiResponse<T[]>> => {
    return sendRequest<T[]>('get', ApiPaths.IMPORT_PREVIEW, config);
  };
  
  /**
   * 导入数据
   * 
   * 用于正式导入大量数据，通常在预览验证通过后调用
   * 
   * @param {ApiRequestConfig} config - 请求配置，通常包含文件ID和导入选项
   * @returns {Promise<ApiResponse<any>>} 返回导入结果，可能包含成功和失败的统计信息
   * 
   */
  const importBigData = async (
    config?: ApiRequestConfig
  ): Promise<ApiResponse<any>> => {
    return sendRequest<any>('get', ApiPaths.IMPORT_DATA, config);
  };
  
  /**
   * 导出数据
   * 
   * 用于导出数据为文件，返回二进制文件流，需要进一步处理为下载
   * 
   * @param {ApiRequestConfig} config - 请求配置，通常包含查询条件和导出参数
   * @returns {Promise<Blob>} 返回导出的文件流，可用于创建下载链接
   */
  const exportData = async (
    config?: ApiRequestConfig
  ): Promise<Blob> => {
    const url = processUrl(ApiPaths.EXPORT, prefix, config?.isFullPath ?? isFullPath);
    
    // 提取请求选项
    const { requestOptions, ...axiosConfig } = config || {};
    
    // 使用defHttp直接处理blob响应
    return await defHttp.get<Blob>({ 
      url,
      ...axiosConfig,
      headers: { ...defaultHeaders, ...(axiosConfig?.headers || {}) },
      responseType: 'blob'
    }, requestOptions);
  };

  /**
   * 创建API实例
   * 
   * 用于创建新的API实例，便于多个API前缀的管理和模块化使用
   * 
   * @param {string} newPrefix - API前缀路径，会自动添加到所有相对URL前
   * @param {boolean} newIsFullPath - 是否使用完整路径模式，默认为false
   * @param {Record<string, string>} newDefaultHeaders - 默认请求头，会添加到所有请求中
   * @returns {Object} 新的API操作方法集合，包含与当前实例相同的方法
   * 
   * @example
   * // 基本用法
   * const userApi = useBaseApi('/api/v1');
   * 
   * // 创建子模块API
   * const orderApi = userApi.createApi('/api/v1/orders');
   * const productApi = userApi.createApi('/api/v1/products');
   * 
   * // 创建带认证的API
   * const authApi = userApi.createApi(
   *   '/api/v1/admin',
   *   false,
   *   { 'Authorization': `Bearer ${token}` }
   * );
   * 
   * // 使用创建的API
   * const orders = await orderApi.getList();
   * const product = await productApi.getDetail(123);
   * const result = await authApi.save({ name: '受限资源' });
   */
  const createApi = (
    newPrefix: string,
    newIsFullPath: boolean = false,
    newDefaultHeaders: Record<string, string> = {}
  ) => {
    return useBaseApi(
      newPrefix,
      newIsFullPath,
      newDefaultHeaders
    );
  };

  return {
    getList,
    getListAll,
    getDetail,
    edit,
    save,
    batchSave,
    remove,
    batchRemove,
    importPreview,
    importBigData,
    exportData,
    request: sendRequest,
    createApi,
    cancelAllRequests
  };
}
