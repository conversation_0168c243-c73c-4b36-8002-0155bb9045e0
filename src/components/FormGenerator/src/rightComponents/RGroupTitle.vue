<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2024-12-03 15:06:45
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-18 15:42:47
-->
<template>
  <a-form-item label="是否为锚点标题">
    <mtcn-radio
      v-model:value="activeData.isAnchor"
      :options="affixOptions"
      optionType="button"
      button-style="solid"
      class="right-radio"
      @click="handelChange" />
  </a-form-item>
  <a-form-item label="锚点ID" v-if="activeData.isAnchor == '1'">
    <mtcn-i18n-input v-model:value="activeData.id" v-model:i18n="activeData.contentI18nCode" placeholder="请输入" />
  </a-form-item>
  <a-form-item label="分组标题">
    <mtcn-i18n-input v-model:value="activeData.content" v-model:i18n="activeData.contentI18nCode" placeholder="请输入" />
  </a-form-item>
  <a-form-item label="标题提示">
    <mtcn-i18n-input v-model:value="activeData.helpMessage" v-model:i18n="activeData.helpMessageI18nCode" placeholder="请输入" />
  </a-form-item>
  <a-form-item label="标签对齐">
    <mtcn-radio v-model:value="activeData.contentPosition" :options="positionOptions" optionType="button" button-style="solid" class="right-radio" />
  </a-form-item>
</template>
<script lang="ts" setup>
  defineOptions({ inheritAttrs: false });
  defineProps(['activeData']);

  const affixOptions = [
    { id: '1', fullName: '是' },
    { id: '0', fullName: '否' },
  ];
  const positionOptions = [
    { id: 'left', fullName: '左对齐' },
    { id: 'center', fullName: '居中对齐' },
    { id: 'right', fullName: '右对齐' },
  ];
  const handelChange = e => {
    props.activeData.id = e.target.value == '1' ? props.activeData.id : '';
  };
</script>
