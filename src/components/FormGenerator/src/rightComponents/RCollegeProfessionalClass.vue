<template>
  <a-form-item label="格式">
    <mtcn-radio v-model:value="activeData.level" :options="levelOptions" class="level-radio" @change="onChange" />
  </a-form-item>
  <a-form-item label="能否清空">
    <a-switch v-model:checked="activeData.clearable" />
  </a-form-item>
</template>
<script lang="ts" setup>
  defineOptions({ inheritAttrs: false });
  const props = defineProps(['activeData']);

  const levelOptions = [
    { id: 0, fullName: '学院', linkageLevel: 1, structureType: 'DEPARTMENT' },
    { id: 1, fullName: '专业', linkageLevel: 1, structureType: 'FACULTY' },
    { id: 2, fullName: '班级', linkageLevel: 1, structureType: 'CLASS' },
    { id: 3, fullName: '学院-专业', linkageLevel: 2, structureType: 'DEPARTMENT_FACULTY' },
    { id: 4, fullName: '学院-专业-班级', linkageLevel: 3, structureType: 'DEPARTMENT_FACULTY_CLASS' },
    { id: 5, fullName: '专业-班级', linkageLevel: 2, structureType: 'FACULTY_CLASS' },
  ];

  function onChange(val, e) {
    props.activeData.linkageLevel = e.linkageLevel;
    props.activeData.structureType = e.structureType;
    props.activeData.__config__.label = e.fullName;
    props.activeData.__config__.defaultValue = [];
    props.activeData.__config__.renderKey = +new Date();
  }
</script>
<style lang="less" scoped>
  .level-radio {
    :deep(.ant-radio-wrapper) {
      display: block;
    }
  }
</style>
