<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-03-05 17:49:16
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-19 11:45:43
-->
<template>
  <!-- <a-form-item label="标签导航方向">
    <mtcn-radio
      v-model:value="activeData.componentSetting.direction"
      :options="directionOptions"
      optionType="button"
      button-style="solid"
      class="right-radio" />
  </a-form-item>
  <a-form-item label="标签是否悬浮">
    <mtcn-radio v-model:value="activeData.componentSetting.affix" :options="affixOptions" optionType="button" button-style="solid" class="right-radio" />
  </a-form-item> -->
  <a-divider>锚点配置</a-divider>
  <a-alert class="mt-10px mb-10px" message="锚点组件需要和分组标题配合使用，锚点名称和分组标题、锚点ID和分组ID 需要保持一致" type="warning" show-icon />
  <div class="options-list">
    <draggable v-model="activeData.componentSetting.children" :animation="300" group="selectItem" handle=".option-drag" itemKey="name">
      <template #item="{ element, index }">
        <div class="select-item">
          <div class="select-line-icon option-drag">
            <i class="icon-ym icon-ym-darg" />
          </div>
          <div class="anchor-item">
            <div class="">名称：</div>
            <mtcn-input class="flex-1" v-model:value="element.title" placeholder="锚点名称" />
            <div class="ml-5px">ID：</div>
            <mtcn-input class="flex-1" v-model:value="element.id" placeholder="锚点ID" @change="e => (element.href = `#${element.id}`)" />
          </div>

          <div class="close-btn select-line-icon" @click="delItem(index, element)">
            <i class="icon-ym icon-ym-btn-clearn" />
          </div>
        </div>
      </template>
    </draggable>
    <div class="add-btn">
      <a-button type="link" preIcon="icon-ym icon-ym-btn-add" @click="addItem">添加锚点</a-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { buildBitUUID } from '@/utils/uuid';
  import draggable from 'vuedraggable';

  const props = defineProps(['activeData']);

  const directionOptions = [
    { id: 'vertical', fullName: '垂直' },
    { id: 'horizontal', fullName: '水平' },
  ];
  const affixOptions = [
    { id: '1', fullName: '是' },
    { id: '0', fullName: '否' },
  ];
  const positionOptions = [
    { id: 'left', fullName: '左侧' },
    { id: 'right', fullName: '右侧' },
  ];
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();

  function delItem(index, item) {
    let list = props.activeData.componentSetting.children;
    let length = list.length;
    if (length < 2) return createMessage.warning('最后一项不能删除');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '删除后不能撤销，确定要删除吗?',
      onOk: () => {
        if (props.activeData.componentSetting.active === item.name) {
          let nextTab = list[index + 1] || list[index - 1];
          if (nextTab) props.activeData.componentSetting.active = nextTab.name;
        }
        props.activeData.componentSetting.children.splice(index, 1);
      },
    });
  }
  function addItem() {
    const uuid = buildBitUUID();
    props.activeData.componentSetting.children.push({
      key: '',
      href: '',
      title: '',
    });
  }
</script>
<style lang="less" scoped>
  .anchor-item {
    display: flex;
    align-items: center;
  }
</style>
