<template>
  <div class="custom-anchor">
    <div class="custom-anchor-links">
      <div
        v-for="item in componentSetting.children"
        :key="item.key"
        class="custom-anchor-link"
        :class="{ 'custom-anchor-link-active': activeLink === item.href }"
        @click="handleClick(item.href)">
        {{ item.title }}
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';

  const props = defineProps({
    componentSetting: {
      type: Object,
      default: () => ({
        direction: 'vertical',
        affix: '1',
        children: [
          {
            title: '锚点1',
            href: '#anchor1',
            key: 'anchor1',
          },
          {
            title: '锚点2',
            key: 'anchor2',
            href: '#anchor2',
          },
        ],
      }),
    },
  });

  const activeLink = ref('');

  const handleClick = href => {
    const el = document.querySelector(href);
    if (el) {
      el.scrollIntoView({ behavior: 'smooth' });
    }
    activeLink.value = href;
  };
  const handleScroll = event => {
    const sections = document.querySelectorAll('[name^="anchor_"]');
    const parent = event.target; // 滚动容器

    let activeSection = null;
    for (let i = 0; i < sections.length; i++) {
      const myElement = sections[i];
      const myElementTop = myElement.getBoundingClientRect().top; // myElement 相对于视口顶部的距离
      const myElementBottom = myElementTop + myElement.offsetHeight; // myElement 相对于视口底部的距离
      // 检查 myElement 是否在滚动容器的可视区内
      if (myElementTop >= parent.scrollTop && myElementBottom <= parent.scrollTop + parent.clientHeight) {
        activeSection = myElement;
        break;
      }
    }

    if (activeSection) {
      const key = activeSection.getAttribute('name').split('_')[1];
      activeLink.value = `#${key}`;
    }
  };

  let allScrollWraps = null;

  onMounted(() => {
    nextTick(() => {
      allScrollWraps = document.querySelectorAll('.scrollbar__wrap');
      if (allScrollWraps.length > 0) {
        allScrollWraps.forEach(scrollWrap => {
          scrollWrap.addEventListener('scroll', handleScroll);
        });
      }
    });
  });

  onBeforeUnmount(() => {
    if (allScrollWraps.length > 0) {
      allScrollWraps.forEach(scrollWrap => {
        scrollWrap.removeEventListener('scroll', handleScroll);
      });
    }
  });

  watch(
    () => props.componentSetting,
    val => {
      activeLink.value = val.children[0].href;
    },
    { deep: true },
  );
</script>

<style scoped>
  .custom-anchor {
    display: flex;
    min-width: 100px;
    position: fixed;
  }
  .affterFixed {
    padding: 10px 0;
    width: 100px;
    position: sticky;
  }
  .custom-anchor-links {
    flex-shrink: 0;
    width: 100%;
    border-left: 1px solid #eaeaea;
  }

  .custom-anchor-link {
    padding: 8px 16px;
    cursor: pointer;
    color: #666;
    transition: background-color 0.3s;
  }

  .custom-anchor-link:hover {
    background-color: #f5f5f5;
  }

  .custom-anchor-link-active {
    color: #1890ff;
    background-color: #e6f7ff;
  }

  .custom-anchor-content {
    flex: 1;
    padding: 16px;
  }

  .custom-anchor-section {
    margin-bottom: 24px;
  }
</style>
