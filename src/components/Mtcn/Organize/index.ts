import { withInstall } from '@/utils';
import OrganizeSelect from './src/OrganizeSelect.vue';
import OrganizeSelectAsync from './src/OrganizeSelectAsync.vue';
import DepSelect from './src/DepSelect.vue';
import DepSelectAsync from './src/DepSelectAsync.vue';
import PosSelect from './src/PosSelect.vue';
import GroupSelect from './src/GroupSelect.vue';
import RoleSelect from './src/RoleSelect.vue';
import UserSelect from './src/UserSelect.vue';
import UsersSelect from './src/UsersSelect.vue';
import MTOrganizeSelect from './src/MTOrganizeSelect.vue';

const isAsync = true;

export const MtcnOrganizeSelect = withInstall(isAsync ? OrganizeSelectAsync : OrganizeSelect);
export const MtcnDepSelect = withInstall(isAsync ? DepSelectAsync : DepSelect);
export const MtcnPosSelect = withInstall(PosSelect);
export const MtcnGroupSelect = withInstall(GroupSelect);
export const MtcnRoleSelect = withInstall(RoleSelect);
export const MtcnUserSelect = withInstall(UserSelect);
export const MtcnUsersSelect = withInstall(UsersSelect);
export const MtcnMTOrganizeSelect = withInstall(MTOrganizeSelect);
