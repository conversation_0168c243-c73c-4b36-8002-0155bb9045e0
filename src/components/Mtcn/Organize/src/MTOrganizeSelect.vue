<template>
  <div :class="[$attrs.class, 'select-tag-list']">
    <a-button preIcon="icon-ym icon-ym-btn-add" @click="openSelectModal">{{ modalTitle }}</a-button>
    <div class="select-pane__body" v-if="selectedData.length">
      <ScrollContainer>
        <template v-if="selectedData.length">
          <div v-for="(item, index) in getSelectedTree" :key="index" class="selected-item-user-multiple">
            <template v-if="item.children.length">
              <p class="selected-item-title">
                <i :class="item.icon"></i><span>{{ item.fullName }}</span>
              </p>
              <div v-for="(child, i) in item.children" :key="i" class="selected-item selected-item-user">
                <div class="selected-item-main">
                  <a-avatar :size="36" :src="apiUrl + child.headIcon" class="selected-item-headIcon" v-if="child.type === 'user'"></a-avatar>
                  <div class="selected-item-icon" v-else>{{ (child.fullName || child.name).substring(0, 1) }}</div>
                  <div class="selected-item-text">
                    <p class="name">{{ child.fullName || child.name }}</p>
                    <p class="organize" :title="child.organize">{{ child.organize }}</p>
                  </div>
                  <delete-outlined class="delete-btn" @click="removeMulData(child.id)" />
                </div>
              </div>
            </template>
          </div>
        </template>
      </ScrollContainer>
    </div>
  </div>

  <a-modal
    v-if="visible"
    v-model:open="visible"
    :title="modalTitle"
    :width="800"
    class="transfer-modal"
    @ok="handleSubmit"
    centered
    :maskClosable="false"
    :keyboard="false">
    <template #closeIcon>
      <ModalClose :canFullscreen="false" @cancel="handleCancel" />
    </template>
    <div class="transfer__body">
      <div class="transfer-pane">
        <div class="transfer-pane__tool">
          <a-input-search :placeholder="t('common.enterKeyword')" allowClear v-model:value="pagination.keyword" @search="handleSearch" />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <a-tabs v-model:activeKey="activeKey" :tabBarGutter="10" size="small" class="pane-tabs">
            <a-tab-pane key="1" tab="组织"></a-tab-pane>
            <a-tab-pane key="2" tab="域及用户组"></a-tab-pane>
            <a-tab-pane key="3" tab="岗位"></a-tab-pane>
            <a-tab-pane key="4" tab="用户"></a-tab-pane>
          </a-tabs>
          <!-- 用户 -->
          <template v-if="activeKey === '4'">
            <BasicTree
              v-if="!isAsync"
              class="tree-main"
              ref="treeRef"
              :treeData="treeData"
              :load-data="onLoadData"
              :loading="loading"
              :key="treeKey"
              @select="handleSelect" />
            <ScrollContainer v-loading="loading && pagination.currentPage === 1" v-else ref="infiniteBody">
              <div v-for="item in treeData" :key="item.id" class="selected-item selected-item-user" @click="handleNodeClick(item)">
                <div class="selected-item-main">
                  <a-avatar :size="36" :src="apiUrl + item.headIcon" class="selected-item-headIcon" />
                  <div class="selected-item-text">
                    <p class="name">{{ item.fullName || item.name }}</p>
                    <p class="organize" :title="item.organize">{{ item.organize }}</p>
                  </div>
                </div>
              </div>
              <Empty :image="simpleImage" v-if="!treeData.length" />
            </ScrollContainer>
          </template>
          <BasicTree
            v-if="['1', '2', '3'].includes(activeKey)"
            ref="treeRef"
            class="tree-main"
            :treeData="ableList"
            @select="handleSelect"
            :fieldNames="getFieldNames"
            defaultExpandAll
            :loading="loading" />
        </div>
      </div>
      <div class="transfer-pane">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <template v-if="selectedData.length">
              <div v-for="(item, index) in getSelectedTree" :key="index" class="selected-item-user-multiple">
                <template v-if="item.children.length">
                  <p class="selected-item-title">
                    <i :class="item.icon"></i><span>{{ item.fullName }}</span>
                  </p>
                  <div v-for="(child, i) in item.children" :key="i" class="selected-item selected-item-user">
                    <div class="selected-item-main">
                      <a-avatar :size="36" :src="apiUrl + child.headIcon" class="selected-item-headIcon" v-if="child.type === 'user'"></a-avatar>
                      <div class="selected-item-icon" v-else>{{ (child.fullName || child.name).substring(0, 1) }}</div>
                      <div class="selected-item-text">
                        <p class="name">{{ child.fullName || child.name }}</p>
                        <p class="organize" :title="child.organize">{{ child.organize }}</p>
                      </div>
                      <delete-outlined class="delete-btn" @click="removeMulData(child.id)" />
                    </div>
                  </div>
                </template>
              </div>
            </template>
            <Empty :image="simpleImage" v-if="!selectedData.length" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { getDepartmentSelectorByAuth } from '@/api/permission/organize';
  import { getImUserSelector, getSelectedList } from '@/api/permission/user';
  import { DeleteOutlined } from '@ant-design/icons-vue';
  import { computed, ref, unref, watch, reactive, nextTick, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicTree, TreeActionType } from '@/components/Tree';
  import { ScrollContainer, ScrollActionType } from '@/components/Container';
  import { useGlobSetting } from '@/hooks/setting';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useOrganizeStore } from '@/store/modules/organize';
  import { useMessage } from '@/hooks/web/useMessage';
  import ModalClose from '@/components/Modal/src/components/ModalClose.vue';
  import { Form, Empty, Modal as AModal } from 'ant-design-vue';
  import { cloneDeep, pick } from 'lodash-es';
  import { useAttrs } from '@/hooks/core/useAttrs';
  import { getPermissionGroup, getAuthorizeGroupGrant } from '@/api/permission/permissionGroup';
  import { useDebounceFn } from '@vueuse/core';

  defineOptions({ name: 'MtcnMTOrganizeSelect', inheritAttrs: false });
  const emit = defineEmits(['change']);
  const attrs: any = useAttrs();
  const visible = ref(false);
  const defaultSelectedList = [
    { id: 'department', type: 'department', fullName: '组织', icon: 'icon-ym icon-ym-tree-department1', children: [] },
    { id: 'position', type: 'position', fullName: '岗位', icon: 'icon-ym icon-ym-tree-position1', children: [] },
    { id: 'user', type: 'user', fullName: '用户', icon: 'icon-ym icon-ym-tree-user2', children: [] },
    { id: 'userGroup', type: 'userGroup', fullName: '用户组', icon: 'icon-ym icon-ym-generator-group1', children: [] },
    { id: 'role', type: 'role', fullName: '角色', icon: 'icon-ym icon-ym-generator-role', children: [] },
  ];
  const getSelectBindValue = computed(() => ({
    ...pick(props, ['placeholder', 'disabled', 'size', 'allowClear']),
    open: false,
    mode: props.multiple ? 'multiple' : '',
    showSearch: false,
    showArrow: true,
    class: unref(attrs)?.class ? 'w-full ' + unref(attrs)?.class : 'w-full',
  }));
  const props = defineProps({
    modalTitle: { type: String, default: '选择授权' },
    id: { type: String },
  });
  const { t } = useI18n();
  const { createMessage } = useMessage();
  const organizeStore = useOrganizeStore();
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);
  const treeRef = ref<Nullable<TreeActionType>>(null);
  const innerValue = ref<string | any[] | undefined>([]);
  const selectedData = ref<any[]>([]);
  const selectedIds = ref<any[]>([]);
  const nodeId = ref('0');
  const id = ref(String);
  const treeKey = ref(+new Date());
  const pagination = reactive({
    keyword: '',
    currentPage: 1,
    pageSize: 20,
  });
  const finish = ref<boolean>(false);
  const isAsync = ref<boolean>(false);
  const activeKey = ref('');
  const infiniteBody = ref<Nullable<ScrollActionType>>(null);
  const treeData = ref<any[]>([]);
  const ableList = ref<any[]>([]);
  const loading = ref(false);
  const simpleImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);

  const getSelectedTree = computed(() => {
    const selectedTree: any[] = cloneDeep(defaultSelectedList);
    for (let i = 0; i < selectedData.value.length; i++) {
      const item = selectedData.value[i];
      const type = item.type == 'company' ? 'department' : item.type;
      inner: for (let i = 0; i < selectedTree.length; i++) {
        if (selectedTree[i].type === type) {
          selectedTree[i].children.push({
            ...item,
            type: type,
          });
          break inner;
        }
      }
    }
    return selectedTree;
  });

  const getFieldNames = computed(() =>
    activeKey.value == 2 ? { key: 'id', title: 'name', children: 'children' } : { key: 'id', title: 'fullName', children: 'children' },
  );

  watch(
    () => activeKey.value,
    val => {
      if (!val) return;
      initData();
    },
  );

  async function initData() {
    loading.value = true;
    if (activeKey.value === '4') {
      if (pagination.keyword) nodeId.value = '0';
      getImUserSelector(nodeId.value, pagination).then(res => {
        if (pagination.keyword) {
          if (res.data.list.length < pagination.pageSize) finish.value = true;
          treeData.value = [...treeData.value, ...res.data.list];
        } else {
          treeData.value = res.data.list;
          if (treeData.value.length && nodeId.value == '0') {
            getTree().setExpandedKeys([treeData.value[0].id]);
          }
        }
        loading.value = false;
      });
    } else {
      ableList.value = [];
      if (activeKey.value === '1') {
        const res = await getDepartmentSelectorByAuth();
        ableList.value = res.data.list;
      } else if (activeKey.value === '2') {
        const res = await getPermissionGroup();
        ableList.value = res.data.list;
      } else if (activeKey.value === '3') {
        ableList.value = await organizeStore.getPositionTree();
      }
      getTree().setSearchValue('');
      if (ableList.value.length > 0) {
        getTree().setExpandedKeys([ableList.value[0].id]);
      }
      loading.value = false;
    }
  }

  // 加载用户
  function onLoadData(node) {
    nodeId.value = node.id;
    return new Promise((resolve: (value?: unknown) => void) => {
      getImUserSelector(nodeId.value).then(res => {
        const list = res.data.list;
        getTree().updateNodeByKey(node.eventKey, { children: list, isLeaf: !list.length });
        resolve();
      });
    });
  }
  function getTree() {
    const tree = unref(treeRef);
    if (!tree) {
      throw new Error('tree is null!');
    }
    return tree;
  }
  function handleSubmit() {
    const ids = unref(selectedData).map(o => o.id);
    innerValue.value = ids;
    visible.value = false;
    loading.value = false;
    emit('change', ids);
  }

  function openSelectModal(data = {}) {
    visible.value = true;
    pagination.keyword = '';
    nodeId.value = '0';
    activeKey.value = '1';
    treeData.value = [];
    ableList.value = [];
    finish.value = false;
    selectedData.value = [];
    id.value = data.id || '';
    initData();
  }
  function handleCancel() {
    visible.value = false;
  }

  function onTagClose(i) {
    innerValue.value.splice(i, 1);
    selectedData.value.splice(i, 1);
    handleSubmit();
  }

  function handleSearch(val) {
    treeKey.value = +new Date();
    nodeId.value = '0';
    treeData.value = [];
    pagination.currentPage = 1;
    isAsync.value = !!pagination.keyword;
    finish.value = false;
    if (isAsync.value && activeKey.value === '4') {
      nextTick(() => {
        bindScroll();
      });
    }
    if (activeKey.value === '4') return initData();
    nextTick(() => {
      getTree().setSearchValue(val);
    });
  }

  function handleSelect(keys, type = '') {
    if (!keys.length) return;
    const data = getTree().getSelectedNode(keys[0]);
    if (data?.disabled) return;
    if (activeKey.value === '1') data.type = 'department';
    if (activeKey.value === '4' && data.type == 'department') return;
    handleNodeClick(data);
  }
  function handleNodeClick(data) {
    const usableList = ['company', 'department', 'role', 'position', 'userGroup', 'user', 'system'];
    if (!usableList.includes(data.type)) return;
    const boo = selectedData.value.some(o => o.id === data.id + '--' + data.type);
    if (boo) return;
    const item = cloneDeep(data);
    item.id += '--' + item.type;
    selectedData.value.push(item);
  }
  function removeAll() {
    selectedData.value = [];
    handleSubmit();
  }
  function removeMulData(id) {
    const index = selectedData.value.findIndex(o => o.id == id);
    if (index != -1) selectedData.value.splice(index, 1);
    handleSubmit();
  }

  watch(
    () => props.id,
    () => {
      loadData();
    },
    { immediate: true },
  );

  function loadData() {
    const thatAttrs = attrs.value;
    const itemId = props.id;
    if (itemId != '' && itemId) {
      getAuthorizeGroupGrant({ itemType: thatAttrs.itemType, itemId }).then(res => {
        const { department = [], position = [], user = [], userGroup = [] } = res.data ?? {};
        enhanceIds(department, 'id', 'type');
        enhanceIds(position, 'id', 'type');
        enhanceIds(user, 'id', 'type');
        enhanceIds(userGroup, 'id', 'type');
        selectedData.value = [...department, ...position, ...user, ...userGroup];
        handleSubmit();
      });
    } else {
      selectedData.value = [];
      emit('change', []);
    }
  }
  function enhanceIds(array, idKey, typeKey) {
    array.forEach(item => {
      if (item[idKey] && item[typeKey]) {
        item[idKey] = `${item[idKey]}--${item[typeKey]}`;
      }
    });
  }
</script>
<style lang="less" scoped>
  .selected-item-user-multiple {
    padding: 0 12px;
    position: relative;
    .selected-item-title {
      height: 32px;
      font-size: 14px;
      display: flex;
      align-items: center;
    }
    .selected-item-user {
      width: 100%;
      padding: 0px 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .selected-item-main {
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        height: 50px;
        width: 100%;
        box-sizing: content-box;
      }
    }
    .selected-item-icon {
      width: 36px;
      height: 36px;
      background: linear-gradient(193deg, #a7d6ff 0%, #1990fa 100%);
      border-radius: 50%;
      line-height: 36px;
      color: #ffffff;
      font-size: 14px;
      text-align: center;
    }
    .selected-item-text {
      min-width: 0;
      flex: 1;
      margin-left: 10px;
      .name {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .organize {
        height: 17px;
        line-height: 17px;
        color: #999999;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .select-pane__body {
    margin-top: 5px;
    position: relative;
    width: 100%;
    height: calc(100% - 40px);
    overflow: auto;
    overflow-x: hidden;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
  }
</style>
