import { withInstall } from '@/utils';
import DatePicker from './src/DatePicker.vue';
import DateRange from './src/DateRange.vue';
import TimePicker from './src/TimePicker.vue';
import TimeRange from './src/TimeRange.vue';

export const MtcnDatePicker = withInstall(DatePicker);
export const MtcnDateRange = withInstall(DateRange);
export const MtcnTimePicker = withInstall(TimePicker);
export const MtcnTimeRange = withInstall(TimeRange);
