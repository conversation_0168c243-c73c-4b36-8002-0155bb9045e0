/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-03-17 16:39:11
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-19 15:31:54
 */
export interface FieldNames {
  label?: string;
  value?: string;
  disabled?: string;
  level?: number;
  linkageLevel?: number; // 联动层级，默认为1
}

export const selectProps = {
  value: {
    type: [String, Number, Array] as PropType<String | number | string[] | number[] | [string, number][]>,
  },
  type: { type: String, default: '1' },
  fieldNames: {
    type: Object as PropType<FieldNames>,
    default: () => ({ value: 'value', label: 'label', children: 'children', disabled: 'disabled' }),
  },
  optionFilterProp: { type: String },
  multiple: { type: Boolean, default: false },
  placeholder: { type: String, default: '请选择' },
  level: { type: Number, default: 0 },
  linkageLevel: { type: Number, default: 0 },
  structureType: { type: String, default: 0 },
};
