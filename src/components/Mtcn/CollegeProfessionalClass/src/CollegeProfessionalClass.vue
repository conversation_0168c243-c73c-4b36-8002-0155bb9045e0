<!--
 * @Description:  学院、专业、班级选择器
 * @Autor: panmy
 * @Date: 2025-03-17 16:31:53
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-19 15:43:19
-->
<template>
  <a-select v-if="props.linkageLevel == 1" v-bind="getBindValue" v-model:value="innerValue" @change="onChange" ref="CollegeProfessionalClassRef" />
  <a-cascader v-else v-bind="getBindValue" v-model:value="innerValue" @change="onChange" ref="CollegeProfessionalClassRef" />
</template>

<script lang="ts" setup>
  import { computed, ref, unref, watch } from 'vue';
  import { selectProps, FieldNames } from './props';
  import { useAttrs } from '@/hooks/core/useAttrs';
  import * as schoolApi from '@/api/publicBase/school';

  defineOptions({ name: 'MtcnSelect', inheritAttrs: false });
  defineExpose({ getCollegeProfessionalClassRef });
  const props = defineProps(selectProps);
  const emit = defineEmits(['update:value', 'change']);
  const attrs = useAttrs({ excludeDefaultKeys: false });
  const innerValue = ref('');
  const CollegeProfessionalClassRef = ref(null);
  const options = ref([]);

  const getFieldNames = computed((): Required<FieldNames> => {
    const { fieldNames } = props;
    return {
      label: 'fullName',
      value: 'id',
      disabled: 'disabled',
      ...fieldNames,
    };
  });

  const getOptionFilterProp = computed(() => props.optionFilterProp || unref(getFieldNames).label);
  const getBindValue = computed(() => ({
    ...unref(attrs),
    ...props,
    showSearch: true,
    options: options.value,
    optionFilterProp: unref(getOptionFilterProp),
    fieldNames: unref(getFieldNames),
    mode: props.multiple ? 'multiple' : '',
    showArrow: Reflect.has(unref(attrs), 'showArrow') ? (unref(attrs) as { showArrow: boolean }).showArrow : true,
    getPopupContainer: () => document.body,
  }));

  watch(
    () => props.value,
    val => {
      setValue(val);
    },
    { immediate: true },
  );

  watch(
    () => props.structureType,
    val => {
      loadOptionsInfo(val);
      innerValue.value = '';
    },
    { immediate: true },
  );
  function setValue(value) {
    innerValue.value = value || value === 0 ? value : undefined;
  }
  function onChange(val, option) {
    emit('update:value', val);
    emit('change', val, option);
  }
  function getCollegeProfessionalClassRef() {
    const select = unref(CollegeProfessionalClassRef);
    if (!select) {
      throw new Error('select is null!');
    }
    return select;
  }

  // 加载下拉列表数据
  async function loadOptionsInfo(structureType: String): void {
    const { data } = await schoolApi.getShoolStructureTypeList({ structureType });
    options.value = data;
  }
</script>
