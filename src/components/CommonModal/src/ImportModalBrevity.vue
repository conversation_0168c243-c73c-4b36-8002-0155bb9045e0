<!--
 * @Description: 导入-简洁版（没有预览、导入数据）
 * @Autor: panmy
 * @Date: 2025-05-13 17:45:27
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-07 22:14:55
-->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="1000"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="mtcn-import-modal">
    <div class="import-main">
      <div class="upload">
        <div class="up_left">
          <img src="../../../assets/images/upload.png" />
        </div>
        <div class="up_right">
          <p class="title">上传填好的数据表</p>
          <p class="tip">文件后缀名必须是xls或xlsx</p>
          <a-upload
            v-model:file-list="fileList"
            class="upload-area"
            accept=".xls,.xlsx"
            :max-count="1"
            :before-upload="beforeUpload"
            @remove="handleFileRemove">
            <a-button type="link">上传文件</a-button>
          </a-upload>
        </div>
      </div>
      <div class="upload">
        <div class="up_left">
          <img src="../../../assets/images/import.png" />
        </div>
        <div class="up_right">
          <p class="title">填写导入数据信息</p>
          <p class="tip">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除</p>
          <a-button type="link" @click="handleTemplateDownload()">下载模板</a-button>
        </div>
      </div>
    </div>
    <template #insertFooter>
      <a-button @click="handleClose(true)">关闭</a-button>
      <a-button type="primary" @click="handleNext" :loading="btnLoading">导入</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive, toRefs, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { getTemplateDownload, getTemplateDownloadAllUrl, getDownloadUrl } from '@/api/basic/common';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { downloadByUrl, downloadByData } from '@/utils/file/download';
  import { useGlobSetting } from '@/hooks/setting';
  import { Upload as AUpload } from 'ant-design-vue';
  import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
  import { upload } from '@/api/basic/common';

  interface State {
    activeStep: number;
    fileName: string;
    fileList: UploadFile[];
    btnLoading: boolean;
    list: any[];
    result: any;
    resultList: any[];
    actionUrl: string;
    apiUrl: string;
    columns: any[];
    resultColumns: any[];
    columnsChildList: any[];
    resultColumnsChildList: any[];
    flowId: string;
    menuId: string;
    downloadTemplateUrl: any;
    isBigFile: boolean;
  }
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['register', 'reload']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const noColumn = { title: '序号', dataIndex: 'index', align: 'center', width: 50, customRender: ({ index }) => index + 1 };
  const actionColumn = { title: '操作', dataIndex: 'action', align: 'center', width: 50, fixed: 'right' };
  const errorColumn = { title: '异常原因', dataIndex: 'errorsInfo', width: 150, fixed: 'right' };

  const globSetting = useGlobSetting();
  const state = reactive<State>({
    activeStep: 0,
    fileName: '',
    fileList: [],
    btnLoading: false,
    list: [],
    result: {},
    resultList: [],
    actionUrl: '',
    apiUrl: '',
    columns: [],
    resultColumns: [],
    columnsChildList: [],
    resultColumnsChildList: [],
    flowId: '',
    menuId: '',
    queryObj: {}, // 导入接口的参数
    downloadTemplateUrl: undefined, // 下载模板接口
    isBigFile: false, // 是否大文件导入
  });
  const { activeStep, fileName, fileList, btnLoading, list, result, resultList, columns, resultColumns } = toRefs(state);

  const getAction = computed(() => globSetting.apiUrl + state.actionUrl);
  const getHeaders = computed(() => ({ Authorization: getToken() as string }));

  function init(data) {
    state.actionUrl = data?.actionUrl ? data.actionUrl : `/api/${data.url || 'visualdev/OnlineDev'}/Uploader`;
    state.apiUrl = data.url || `visualdev/OnlineDev/${data.modelId}`;
    state.queryObj = data.queryObj || {};
    state.downloadTemplateUrl = data?.downloadTemplateUrl || undefined;
    state.isBigFile = data?.isBigFile || false;
    state.flowId = data.flowId || '';
    state.activeStep = 0;
    state.fileList = [];
    state.btnLoading = false;
  }

  function handleNext() {
    if (!state.fileList.length) return createMessage.warning('请先上传文件');
    handleSubmit();
  }

  async function handleSubmit() {
    try {
      if (!state.actionUrl) return;
      const formData = new FormData();

      formData.append('file', state.fileList[0].originFileObj);
      for (let [key, value] of Object.entries(state.queryObj || {})) {
        formData.append(key, value);
      }
      state.btnLoading = true;
      upload(state.actionUrl, formData)
        .then(res => {
          if (res.data.code !== 200) throw new Error(res.data.msg);
          // 检查响应头中是否包含content-disposition
          const contentDisposition = res.headers?.['content-disposition'];

          if (contentDisposition) {
            // 如果是文件流，使用downloadByData
            const filenameRegex = /filename\*=utf-8''(.+)/;
            const match = contentDisposition.match(filenameRegex);
            let decodedFilename = '';
            if (match && match[1]) {
              decodedFilename = decodeURIComponent(match[1]);
            }
            downloadByData(res.data, decodedFilename);
          } else {
            const data = res?.data?.data || res?.data;
            if (data.code != 200) {
              createMessage.error(res.data.msg);
              if (data?.type) {
                getDownloadUrl(data?.type, data?.fileName).then(e => {
                  downloadByUrl({ url: e.data.url, fileName: data.rawFileName });
                });
              }
            } else {
              createMessage.success(data.msg);
            }
            handleClose(true);
          }

          state.btnLoading = false;
        })
        .catch(e => {
          createMessage.error(e || e.msg || '导入失败');
          state.btnLoading = false;
        });
    } catch (_) {}
  }

  function beforeUpload(file) {
    const fileType = file.name.replace(/.+\./, '');
    const isAccept = ['xls', 'xlsx'].indexOf(fileType.toLowerCase()) !== -1;
    if (!isAccept) {
      createMessage.error('文件格式不正确');
      return AUpload.LIST_IGNORE;
    }
    const isRightSize = file.size / 1024 < 500;
    if (!isRightSize && !state.isBigFile) {
      createMessage.error('文件大小超过500KB');
      return AUpload.LIST_IGNORE;
    }
    return false;
  }
  function handleFileRemove(file) {
    return new Promise<void>((resolve, reject) => {
      createConfirm({
        iconType: 'warning',
        title: t('common.tipTitle'),
        content: `确定移除${file.name}?`,
        onOk: () => {
          resolve();
        },
        onCancel: () => {
          reject();
        },
      });
    });
  }
  function handleTemplateDownload() {
    if (state.downloadTemplateUrl) {
      getTemplateDownloadAllUrl(state.downloadTemplateUrl, {}).then(response => {
        const contentDisposition = response.headers['content-disposition'];
        const filenameRegex = /filename\*=utf-8''(.+)/;
        const match = contentDisposition.match(filenameRegex);
        let decodedFilename = '';
        if (match && match[1]) {
          decodedFilename = decodeURIComponent(match[1]);
        }
        downloadByData(response.data, decodedFilename);
      });
    } else {
      getTemplateDownload(state.apiUrl, { menuId: state.menuId }).then(res => {
        const contentDisposition = res.headers?.['content-disposition'];
        if (contentDisposition) {
          const filenameRegex = /filename\*=utf-8''(.+)/;
          const match = contentDisposition.match(filenameRegex);
          let decodedFilename = '';
          if (match && match[1]) {
            decodedFilename = decodeURIComponent(match[1]);
          }
          downloadByData(res.data, decodedFilename);
        } else {
          downloadByUrl({ url: res.data?.url });
        }
      });
    }
  }

  function handleClose(reload = false) {
    state.btnLoading = false;
    closeModal();
    if (reload) emit('reload');
  }
</script>
