<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-03-17 09:11:51
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-10 17:27:51
-->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="t('common.exportText')"
    :okText="t('common.exportText')"
    @ok="handleSubmit"
    destroyOnClose
    class="export-modal">
    <template #insertFooter>
      <div class="footer-tip">提示:系统将导出列表中选中的数据</div>
    </template>
    <a-form :colon="false" labelAlign="left" :labelCol="{ style: { width: '90px' } }">
      <div class="export-line">
        <p class="export-label">导出方式</p>
      </div>
      <a-form-item>
        <a-radio-group v-model:value="dataType">
          <a-radio :value="0">当前页面数据</a-radio>
          <a-radio :value="1">全部页面数据</a-radio>
          <a-radio :value="2" :disabled="!selectIds || !selectIds.length" v-if="webType != '4'">当前选择数据</a-radio>
        </a-radio-group>
      </a-form-item>
      <div class="export-line">
        <p class="export-label">导出数据<span>请选择导出字段</span></p>
      </div>
      <a-checkbox :indeterminate="isIndeterminate" v-model:checked="checkAll" @change="handleCheckAllChange">全选</a-checkbox>
      <a-checkbox-group v-model:value="checkedList" class="options-list" @change="handleCheckedChange">
        <a-checkbox v-for="item in columnList" :key="item.id" :value="item.id" class="options-item">
          {{ item?.fullName || item?.title }}
        </a-checkbox>
      </a-checkbox-group>
    </a-form>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, inject } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { noGroupList } from '@/components/FormGenerator/src/helper/config';
  import { cloneDeep } from 'lodash-es';

  import { debug } from 'console';

  const useBaseApi = inject('useBaseApi');
  const exportApi = useBaseApi('/api/export/config');
  const emit = defineEmits(['register', 'download']);
  const [registerModal, { changeOkLoading }] = useModalInner(init);
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const dataType = ref(0);
  const webType = ref('');
  const selectIds = ref([]);
  const isIndeterminate = ref(false);
  const checkAll = ref(false);
  const columnList = ref<any[]>([]);
  const checkedList = ref<string[]>([]);
  const defaultCheckedList = ref<string[]>([]);
  // 20250710 如果设置 exportType 需要去低代码配置导出字段
  const exportType = ref(undefined);

  async function init(data) {
    webType.value = data.webType || '';
    exportType.value = data?.exportType || undefined;
    columnList.value = !exportType.value
      ? cloneDeep(data.columnList).filter(o => !noGroupList.includes(o?.__config__?.jnpfKey))
      : await getSetColumnField({ ...data.listQuery, exportType: data.exportType });
    selectIds.value = data.selectIds || [];
    dataType.value = 0;
    checkedList.value = columnList.value.map(o => o.id);
    handleCheckedChange(checkedList.value);
  }
  async function getSetColumnField(params) {
    const res = await exportApi.request('get', '/exportConfig', {
      params,
    });
    if (!res.data || !res.data?.fieldsMap) return createMessage.error('未查到字段数据!');
    const fieldsMap = JSON.parse(res.data.fieldsMap);
    const fields = Object.entries(fieldsMap).map(([id, fullName]) => ({
      id,
      fullName,
    }));
    return fields;
  }
  function handleCheckAllChange(e) {
    const val = e.target.checked;
    checkedList.value = val ? columnList.value.map(o => o.id) : defaultCheckedList.value;
    isIndeterminate.value = val ? false : !!defaultCheckedList.value.length;
  }
  function handleCheckedChange(val) {
    const checkedCount = val.length;
    checkAll.value = checkedCount === columnList.value.length;
    isIndeterminate.value = !!checkedCount && checkedCount < columnList.value.length;
  }
  function getIntersectionAsObject(arr1, arr2) {
    return arr1.reduce((result, key) => {
      const item = arr2.find(item => item.id === key);
      if (item) {
        result[key] = item.fullName || item.title;
      }
      return result;
    }, {});
  }
  function handleSubmit() {
    if (!checkedList.value.length) return createMessage.warning('请至少选择一个导出字段');
    changeOkLoading(true);
    const data = { dataType: dataType.value, selectKey: checkedList.value, selectIds: selectIds.value };
    if (exportType.value) {
      data.selectKey = getIntersectionAsObject(data.selectKey, columnList.value);
    }
    emit('download', data);
  }
</script>
