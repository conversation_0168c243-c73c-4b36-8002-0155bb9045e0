import { withInstall } from '@/utils';
import billRuleModal from './src/BillRuleModal.vue';
import dataSetModal from './src/DataSetModal.vue';
import exportModal from './src/ExportModal.vue';
import importModal from './src/ImportModal.vue';
import importModalBrevity from './src/ImportModalBrevity.vue';
import interfaceModal from './src/InterfaceModal.vue';
import previewModal from './src/PreviewModal.vue';
import selectFlowModal from './src/SelectFlowModal.vue';
import selectModal from './src/SelectModal.vue';
import superQueryModal from './src/SuperQueryModal.vue';

export const InterfaceModal = withInstall(interfaceModal);
export const BillRuleModal = withInstall(billRuleModal);
export const SelectModal = withInstall(selectModal);
export const PreviewModal = withInstall(previewModal);
export const ExportModal = withInstall(exportModal);
export const ImportModal = withInstall(importModal);
export const ImportModalBrevity = withInstall(importModalBrevity);

export const SuperQueryModal = withInstall(superQueryModal);
export const SelectFlowModal = withInstall(selectFlowModal);
export const DataSetModal = withInstall(dataSetModal);
