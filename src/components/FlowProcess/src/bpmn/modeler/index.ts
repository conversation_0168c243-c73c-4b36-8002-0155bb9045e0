import Modeler from 'bpmn-js/lib/Modeler';
import mtcnPaletteProvider from '../palette';
import mtcnRenderer from '../renderer';
import mtcnElementFactory from '../factory';
import mtcnOutline from '../outline';
import mtcnBusinessData from '../business';
import mtcnGridSnappingAutoPlaceBehavior from '../gridSnapping';
import mtcnAlignElementsContextPadProvider from '../alignElements';
import mtcnContextPad from '../contextPad';
import mtcnContextPadProvider from '../contextPad/provider';
import mtcnCustomBpmnRules from '../rule';
import mtcnCommandStack from '../commandStack';
import mtcnCustomBpmnCopyPaste from '../copyPaste';
import GridSnappingLayoutConnectionBehavior from '../gridSnapping/connect';
let flowInfo: any;
const modeler: any = options => [
  {
    __init__: [
      'paletteProvider',
      'bpmnRenderer',
      'contextPadProvider',
      'replaceMenuProvider',
      'elementFactory',
      'mtcnData',
      'gridSnappingAutoPlaceBehavior',
      'alignElementsContextPadProvider',
      'alignElementsMenuProvider',
      'bpmnAlignElements',
      'outlineProvider',
      'contextPad',
      'bpmnRules',
      'bpmnCopyPaste',
    ],
    paletteProvider: ['type', mtcnPaletteProvider], // 左侧的元素 目前不用该方法
    bpmnRenderer: ['type', mtcnRenderer, { options }], // 画布渲染
    elementFactory: ['type', mtcnElementFactory], // 元素工厂
    mtcnData: ['type', mtcnBusinessData], // 用于放置业务数据
    gridSnappingAutoPlaceBehavior: ['type', mtcnGridSnappingAutoPlaceBehavior], // 自动生成元素位置 在点击coontext-pad时计算元素生成位置
    alignElementsContextPadProvider: ['type', mtcnAlignElementsContextPadProvider], // 元素的排序等
    outlineProvider: ['type', mtcnOutline], // 元素的外边框(用于修改边框颜色，注：线条颜色有svg获取标签再去修改颜色及箭头）
    contextPad: ['type', mtcnContextPad], // 点击元素后的元素右侧弹窗框（显示开始节点 结束节点等）
    contextPadProvider: ['type', mtcnContextPadProvider], // context-pad 属性
    bpmnRules: ['type', mtcnCustomBpmnRules], // 自定义规则
    commandStack: ['type', mtcnCommandStack], // 自定义CommandStack
    gridSnappingLayoutConnectionBehavior: ['type', GridSnappingLayoutConnectionBehavior], // 修改连线的排序
    bpmnCopyPaste: ['type', mtcnCustomBpmnCopyPaste], // 复制元素
  },
];

class bpmnModeler extends Modeler {
  constructor(options: any) {
    flowInfo = options.flowInfo;
    super(options);
  }
}

bpmnModeler.prototype['_modules'] = [].concat(bpmnModeler.prototype['_modules'], modeler(flowInfo));

export default bpmnModeler;
