import { mtcnApproverConfig } from './element/approver';
import { mtcnStartConfig } from './element/start';
import { mtcnEndConfig } from './element/end';
import { mtcnSubFlowConfig } from './element/subFlow';
import { mtcnTimerConfig } from './element/timer';
import { mtcnLabelConfig } from './element/label';
import { mtcnExclusiveConfig } from './element/gateway/exclusive';
import { mtcnInclusiveConfig } from './element/gateway/inclusive';
import { mtcnParallelConfig } from './element/gateway/parallel';
import {
  bpmnTask,
  bpmnStart,
  bpmnEnd,
  bpmnTimer,
  bpmnSubFlow,
  bpmnLabel,
  bpmnInclusive,
  bpmnParallel,
  bpmnExclusive,
  typeStart,
  typeEnd,
  typeSubFlow,
  typeTimer,
  typeLabel,
  typeGateway,
  typeTask,
  bpmnSequenceFlow,
} from './variableName';
import { mtcnSequenceFlow } from './element/sequenceFlow';

const hasLabelElements: any = ['bpmn:StartEvent', 'bpmn:EndEvent', 'bpmn:InclusiveGateway']; // 一开始就有label标签的元素类型
const BpmnBusinessObjectKey = {
  id: 'wnId',
};

const typeConfig: any = {
  [bpmnTask]: mtcnApproverConfig,
  [bpmnStart]: mtcnStartConfig,
  [bpmnEnd]: mtcnEndConfig,
  [bpmnSubFlow]: mtcnSubFlowConfig,
  [bpmnTimer]: mtcnTimerConfig,
  [bpmnLabel]: mtcnLabelConfig,
  [bpmnInclusive]: mtcnInclusiveConfig,
  [bpmnParallel]: mtcnParallelConfig,
  [bpmnExclusive]: mtcnExclusiveConfig,
  [bpmnSequenceFlow]: mtcnSequenceFlow,
};

// 默认wnType值
const conversionWnType: any = {
  [bpmnStart]: typeStart,
  [bpmnEnd]: typeEnd,
  [bpmnTask]: typeTask,
  [bpmnSubFlow]: typeSubFlow,
  [bpmnTimer]: typeTimer,
  [bpmnLabel]: typeLabel,
  [bpmnInclusive]: typeGateway,
  [bpmnParallel]: typeGateway,
  [bpmnExclusive]: typeGateway,
};

export { typeConfig, BpmnBusinessObjectKey, hasLabelElements, conversionWnType };
