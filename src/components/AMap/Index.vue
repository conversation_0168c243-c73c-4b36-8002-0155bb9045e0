<template>
  <div>
    <a-select
      v-if="!props.isViewMode"
      v-model:value="state.keyword"
      show-search
      placeholder="搜索或者直接在地图上点选"
      :default-active-first-option="false"
      :show-arrow="false"
      :filter-option="false"
      :not-found-content="null"
      :fieldNames="{ label: 'address', value: 'id', location: 'location' }"
      :options="options"
      @search="handleSearch"
      @change="handleChange"></a-select>
    <div v-else class="position">
      <img :src="mark" alt="" srcset="" />
      {{ state.keyword }}
    </div>
    <div class="map-contain">
      <div id="container" class="map"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, reactive, watch, nextTick } from 'vue';
  import AMapLoader from '@amap/amap-jsapi-loader';
  import { useGlobSetting } from '@/hooks/setting';
  import mark from '@/assets/images/mark.png';

  const globSetting = useGlobSetting();
  const mapContainer = ref(null);
  const options = ref([]);

  (window as any)._AMapSecurityConfig = {
    securityJsCode: globSetting.aMapSecurityJsCode,
  };

  interface State {
    visible: boolean;
    keyword: string;
    location: any;
    AMap: any;
    map: any;
  }

  const props = defineProps({
    // 纬度
    lat: {
      type: String,
      default: '',
    },
    // 经度
    lng: {
      type: String,
      default: '',
    },
    // 是否是查看模式
    isViewMode: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['handleMap']);
  const state = reactive<State>({
    visible: false,
    keyword: '',
    location: {
      lat: '',
      lng: '',
    },
    AMap: null,
    map: null,
  });
  // 标志点
  let marker = null;
  // 坐标 转地址
  let geocoder = null;
  // 获取输入提示信息
  let Autocomplete = null;

  // 初始化地图
  async function initMap() {
    AMapLoader.load({
      key: globSetting.aMapJsKey,
      version: '1.4.15',
      plugins: ['AMap.Geocoder', 'AMap.Autocomplete'],
    }).then(AMap => {
      state.AMap = AMap;
      state.map = new AMap.Map('container', {
        resizeEnable: true,
        viewMode: '3D',
        zoom: 16,
        dragEnable: props.isViewMode,
      });

      geocoder = new AMap.Geocoder({
        city: '010',
        radius: 500,
      });
      marker = new AMap.Marker({ position: [0, 0] });
      Autocomplete = new AMap.Autocomplete({ city: '010' });
      state.map.add(marker);
      if (!props.isViewMode) {
        state.map.on('click', function (e) {
          state.location = {
            lat: e.lnglat.getLat(),
            lng: e.lnglat.getLng(),
          };
          regeoCode(state.location.lng, state.location.lat);
        });
      }
    });
  }

  // 添加标志点
  function regeoCode(lng, lat) {
    if (!marker) return;
    const position = [parseFloat(lng), parseFloat(lat)];
    marker.setPosition(position);
    state.map.setCenter(position);
    geocoder.getAddress(position, function (status, result) {
      if (status === 'complete' && result.regeocode) {
        const formattedAddress = result.regeocode.formattedAddress;
        state.keyword = formattedAddress;
        emit('handleMap', {
          address: formattedAddress,
          lat: lat,
          lng: lng,
        });
      } else {
        console.error('根据经纬度查询地址失败');
      }
    });
  }

  // #region 搜索

  function handleSearch(e) {
    if (e && e !== '') {
      Autocomplete.search(e, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          options.value = result.tips;
          console.log(result.tips);
        }
      });
    } else {
      options.value = [];
    }
  }
  function handleChange(value, event) {
    if (value) {
      state.location = {
        lat: event.location.lat,
        lng: event.location.lng,
      };
      regeoCode(state.location.lng, state.location.lat);
    }
  }
  // #endregion

  onMounted(() => {
    initMap();
  });

  watch(
    () => [props.lng, props.lat],
    val => {
      if (val[0] !== '' && val[1] !== '') {
        setTimeout(() => {
          state.location = {
            lat: val[1],
            lng: val[0],
          };
          regeoCode(val[0], val[1]);
        }, 300);
      }
    },
    { immediate: true },
  );
</script>

<style scoped lang="less">
  .map-contain {
    position: relative;
    .map {
      margin: 10px 0;
      width: 100%;
      min-height: 300px;
    }
    // .map-marker {
    //   width: 19px;
    //   height: 32px;
    //   position: absolute;
    //   top: 50%;
    //   left: 50%;
    //   transform: translate(-50%, calc(-50% - 9.5px));
    // }
  }

  .position {
    display: flex;
    align-items: center;
    font-size: 14px;
    img {
      width: 10px;
      height: 15px;
      display: block;
      margin-right: 5px;
    }
  }
</style>
