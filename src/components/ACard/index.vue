<template>
  <!-- 页面标题 -->
  <div v-if="showTitle" class="point mr-20px ml-20px">{{ title }}</div>
  <!-- 备注区域 -->
  <div v-if="showRemark" class="fw-tip">
    <slot name="remark"> </slot>
  </div>
  <!-- 页面搜索区 -->
  <div v-if="showSearch" class="fw-tip">
    <Mtcn-Input v-model="keyWord" :placeholder="searchPlaceholder" />
    <a-button @click="handleSearch" type="primary">搜索</a-button>
    <a-button @click="handleReset">重置</a-button>
  </div>

  <!-- 页面公共按钮区 -->
  <div v-if="showButtons" class="fw-tip">
    <slot name="buttons">
      <template v-for="(btn, index) in buttons" :key="index">
        <a-button :type="btn.type" :preIcon="btn.icon" @click="btn.onClick ? btn.onClick() : ''">
          {{ btn.label }}
        </a-button>
      </template>
    </slot>
  </div>

  <!-- 卡片区 -->
  <div v-if="showCardList" class="portalMain">
    <template v-for="(item, index) in cardList" :key="index">
      <div :class="['card', item[statusField] == 1 ? 'cardSel' : '']">
        <div class="cardTop">
          <!-- 卡片标题区 -->
          <p v-if="showCardTitle" class="cardTitle">{{ item.title }}</p>
          <!-- 卡片详情描述区 -->
          <p v-if="showCardSubtitle" class="cardTemplate">
            <slot name="card-subtitle" :item="item"> </slot>
          </p>
        </div>
        <!-- 卡片按钮组区 -->
        <div class="cardOpts">
          <slot name="card-actions" :item="item">
            <!-- 如果按钮数量小于或等于3，全部展示 -->
            <template v-if="actions.length <= 3">
              <template v-for="(btn, btnIndex) in actions" :key="btnIndex">
                <component
                  :is="btn.type === 'switch' ? 'a-switch' : 'a-button'"
                  :type="btn.type === 'switch' ? '' : btn.type"
                  v-model:checked="item[btn.bindField]"
                  v-bind="btn.props"
                  @click="btn.onClick ? btn.onClick(item) : ''">
                  {{ btn.label }}
                </component>
                <div class="split" v-if="btnIndex < actions.length - 1"></div>
                <!-- 在按钮之间添加分隔符 -->
              </template>
            </template>

            <!-- 如果按钮数量超过3，展示前两个按钮，其余放入“更多”下拉菜单 -->
            <template v-else>
              <template v-for="(btn, btnIndex) in actions.slice(0, 2)" :key="btnIndex">
                <component
                  :is="btn.type === 'switch' ? 'a-switch' : 'a-button'"
                  :type="btn.type === 'switch' ? '' : btn.type"
                  v-model:checked="item[btn.bindField]"
                  v-bind="btn.props"
                  @click="btn.onClick ? btn.onClick(item) : ''(btn, item)">
                  {{ btn.label }}
                </component>
                <div class="split" v-if="btnIndex < 1"></div>
                <!-- 只在第一个按钮后添加分隔符 -->
              </template>
              <div class="split" v-if="actions.length > 3"></div>
              <a-dropdown>
                <a-button type="link" @click.prevent>
                  更多
                  <DownOutlined class="ml-5px" />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-for="(btn, btnIndex) in actions.slice(2)" :key="btnIndex + 2" @click="btn.onClick ? btn.onClick(item) : ''(btn, item)">
                      {{ btn.label }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </slot>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { DownOutlined } from '@ant-design/icons-vue';

  const props = defineProps({
    title: {
      type: String,
      default: '页面标题',
    },
    searchPlaceholder: {
      type: String,
      default: '请输入搜索关键词',
    },
    buttons: {
      type: Array,
      default: () => [],
    },
    cardList: {
      type: Array,
      default: () => [],
    },
    actions: {
      type: Array,
      default: () => [],
    },
    statusField: {
      type: String,
      default: 'sfqy', // 默认启用/停用字段名
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    showRemark: {
      type: Boolean,
      default: true,
    },
    showButtons: {
      type: Boolean,
      default: true,
    },
    showCardList: {
      type: Boolean,
      default: true,
    },
    showCardTitle: {
      type: Boolean,
      default: true,
    },
    showCardSubtitle: {
      type: Boolean,
      default: true,
    },
  });
  const keyWord = ref('');

  const emit = defineEmits(['search', 'custom-event']);

  const handleSearch = () => {
    emit('search', keyWord.value);
  };

  const handleReset = () => {
    keyWord.value = '';
  };
</script>
<style lang="less" scoped>
  .mtcn-content-wrapper-content {
    background: #fff;
    position: relative;

    overflow: scroll !important;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .fw-tip {
    margin: 4px 20px 10px 20px;
    color: #666666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    .ant-input {
      width: 300px;
    }
  }
  .portalMain {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

    margin: 20px;
    grid-gap: 10px;
    .card {
      border: 1px solid #edeff2;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
      .cardTop {
        padding: 16px;
        .cardTitle {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          font-size: 16px;
          // font-weight: 700;
          margin-bottom: 16px;
        }
        .cardTemplate {
          margin-top: 4px;
          color: #86909c;
          font-size: 14px;
          line-height: 22px;
          span {
            color: #4e5969;
          }
        }
      }
      .cardOpts {
        padding: 11px 12px;
        border-top: 1px solid #edeff2;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .link {
          border-color: transparent;
          color: @primary-color;
          background: 0 0;
          display: inline-block;
          line-height: 1;
          white-space: nowrap;
          cursor: pointer;
        }
        .split {
          width: 1px;
          height: 12px;
          margin: 0 12px;
          background-color: #dfe2e8;
          flex-shrink: 0;
        }
      }
    }
    .cardSel::after {
      content: '已启用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #00b42a;
    }
  }
</style>
