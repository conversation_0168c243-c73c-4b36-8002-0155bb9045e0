<template>
  <div class="anticon" :class="getAppLogoClass" @click="goHome">
    <template v-if="showTitle">
      <a-image
        v-if="getSysConfig && getSysConfig.logoIcon && getSysConfig.currentSysName == ''"
        class="login-logo login-logo2"
        :src="apiUrl + getSysConfig.logoIcon"
        :fallback="logoImg"
        :preview="false" />
      <div class="logo-wrapper" v-else>
        <img class="login-logo login-logo2" :src="apiUrl + getSysConfig.logoIcon" :fallback="mtcnImg" :preview="false" />
        <span>{{ getSysConfig.currentSysName }}</span>
      </div>
    </template>
    <template v-else>
      <a-image
        class="login-logo login-logo2"
        :src="apiUrl + getSysConfig.logoIcon"
        :fallback="mtcnImg"
        :preview="false"
        v-if="getSysConfig && getSysConfig.logoIcon" />
      <img :src="mtcnImg" v-else />
    </template>
  </div>
</template>
<script lang="ts" setup>
  import mtcnImg from '@/assets/images/mtcn.png';
  import logoImg from '@/assets/images/mtcn2.png';
  import { PageEnum } from '@/enums/pageEnum';
  import { useGlobSetting } from '@/hooks/setting';
  import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
  import { useDesign } from '@/hooks/web/useDesign';
  import { useGo } from '@/hooks/web/usePage';
  import { useAppStore } from '@/store/modules/app';
  import { Image as AImage } from 'ant-design-vue';
  import { computed, ref, unref } from 'vue';

  const props = defineProps({
    /**
     * The theme of the current parent component
     */
    theme: { type: String, validator: (v: string) => ['light', 'dark'].includes(v) },
    /**
     * Whether to show title
     */
    showTitle: { type: Boolean, default: true },
    /**
     * The title is also displayed when the menu is collapsed
     */
    alwaysShowTitle: { type: Boolean },
  });

  const { prefixCls } = useDesign('app-logo');
  const { getCollapsedShowTitle } = useMenuSetting();
  const appStore = useAppStore();
  const globSetting = useGlobSetting();
  const apiUrl = ref(globSetting.apiUrl);
  const go = useGo();

  const getAppLogoClass = computed(() => [prefixCls, props.theme, { 'collapsed-show-title': unref(getCollapsedShowTitle) }]);
  const getSysConfig = computed(() => appStore.getSysConfigInfo);

  function goHome() {
    go(PageEnum.BASE_HOME);
  }
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-app-logo';

  .@{prefix-cls} {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &.collapsed-show-title {
      padding-left: 20px;
    }

    &.light &__title {
      color: @primary-color;
    }

    &.dark &__title {
      color: @white;
    }

    &__title {
      font-size: 16px;
      font-weight: 700;
      transition: all 0.5s;
      line-height: normal;
    }
    :deep(.ant-image),
    .login-logo {
      // width: 100%;
      height: 100%;
      .login-logo {
        width: auto;
        height: 100%;
      }
    }
    .logo-wrapper {
      // width: 100%;
      height: 100%;
      overflow: hidden;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      img {
        &.login-logo2 {
          width: 300px;
          height: 60px;
          object-fit: contain;
          overflow: hidden;
        }
      }
      span {
        display: inline-block;
        min-width: 100px;
        font-size: 16px;
        font-weight: 700;
        // margin-left: 15px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .login-logo1 {
      width: 80%;
      height: 80%;
    }
  }
  .login-logo2 {
    width: auto;
    object-fit: none;
    margin: 0 auto;
    flex-shrink: 0;
  }
  // 抽屉式菜单

  .siderMenu {
    .mtcn-layout-menu-logo {
      height: @sider-Menu-height;
    }
    .mtcn-app-logo .logo-wrapper {
      background-color: #dee3ed !important;
      img.login-logo2 {
        width: 200px;
        object-fit: contain;
      }
    }
  }
</style>
