import type { Component } from 'vue';
import type { ComponentType } from './types/index';

/**
 * Component list, register here to setting it in the form
 */
import { CountdownInput } from '@/components/CountDown';
import { StrengthMeter } from '@/components/StrengthMeter';
// mtcn 组件
import {
  MtcnAlert,
  MtcnAnchor,
  MtcnAreaSelect,
  MtcnAutoComplete,
  MtcnBarcode,
  MtcnButton,
  MtcnCalculate,
  MtcnCascader,
  MtcnCheckbox,
  MtcnCheckboxSingle,
  MtcnCollegeProfessionalClass,
  MtcnColorPicker,
  MtcnCron,
  MtcnDatePicker,
  MtcnDateRange,
  MtcnDepSelect,
  MtcnDivider,
  MtcnEditor,
  MtcnGroupSelect,
  MtcnGroupTitle,
  MtcnIconPicker,
  MtcnIframe,
  MtcnInput,
  MtcnInputGroup,
  MtcnInputNumber,
  MtcnInputPassword,
  MtcnInputSearch,
  MtcnInputTable,
  MtcnLink,
  MtcnLocation,
  MtcnMonthPicker,
  MtcnNumberRange,
  MtcnOpenData,
  MtcnOrganizeSelect,
  MtcnPopupAttr,
  MtcnPopupSelect,
  MtcnPopupTableSelect,
  MtcnPosSelect,
  MtcnQrcode,
  MtcnRadio,
  MtcnRate,
  MtcnRelationForm,
  MtcnRelationFormAttr,
  MtcnRoleSelect,
  MtcnSelect,
  MtcnSign,
  MtcnSignature,
  MtcnSlider,
  MtcnSwitch,
  MtcnText,
  MtcnTextarea,
  MtcnTimePicker,
  MtcnTimeRange,
  MtcnTreeSelect,
  MtcnUploadFile,
  MtcnUploadImg,
  MtcnUploadImgSingle,
  MtcnUserSelect,
  MtcnUsersSelect,
  MtcnWeekPicker,
} from '@/components/Mtcn';

const componentMap = new Map<ComponentType, Component>();

componentMap.set('StrengthMeter', StrengthMeter);
componentMap.set('InputCountDown', CountdownInput);

componentMap.set('InputGroup', MtcnInputGroup);
componentMap.set('InputSearch', MtcnInputSearch);
componentMap.set('MonthPicker', MtcnMonthPicker);
componentMap.set('WeekPicker', MtcnWeekPicker);

componentMap.set('Alert', MtcnAlert);
componentMap.set('AreaSelect', MtcnAreaSelect);
componentMap.set('AutoComplete', MtcnAutoComplete);
componentMap.set('Button', MtcnButton);
componentMap.set('Cron', MtcnCron);
componentMap.set('Cascader', MtcnCascader);
componentMap.set('ColorPicker', MtcnColorPicker);
componentMap.set('Checkbox', MtcnCheckbox);
componentMap.set('MtcnCheckboxSingle', MtcnCheckboxSingle);
componentMap.set('DatePicker', MtcnDatePicker);
componentMap.set('DateRange', MtcnDateRange);
componentMap.set('TimePicker', MtcnTimePicker);
componentMap.set('TimeRange', MtcnTimeRange);
componentMap.set('Divider', MtcnDivider);
componentMap.set('Editor', MtcnEditor);
componentMap.set('GroupTitle', MtcnGroupTitle);
componentMap.set('Input', MtcnInput);
componentMap.set('InputPassword', MtcnInputPassword);
componentMap.set('Textarea', MtcnTextarea);
componentMap.set('InputNumber', MtcnInputNumber);
componentMap.set('IconPicker', MtcnIconPicker);
componentMap.set('Link', MtcnLink);
componentMap.set('OrganizeSelect', MtcnOrganizeSelect);
componentMap.set('DepSelect', MtcnDepSelect);
componentMap.set('PosSelect', MtcnPosSelect);
componentMap.set('GroupSelect', MtcnGroupSelect);
componentMap.set('RoleSelect', MtcnRoleSelect);
componentMap.set('UserSelect', MtcnUserSelect);
componentMap.set('UsersSelect', MtcnUsersSelect);
componentMap.set('Qrcode', MtcnQrcode);
componentMap.set('Barcode', MtcnBarcode);
componentMap.set('Radio', MtcnRadio);
componentMap.set('Rate', MtcnRate);
componentMap.set('Select', MtcnSelect);
componentMap.set('Slider', MtcnSlider);
componentMap.set('Sign', MtcnSign);
componentMap.set('Signature', MtcnSignature);
componentMap.set('Switch', MtcnSwitch);
componentMap.set('Text', MtcnText);
componentMap.set('TreeSelect', MtcnTreeSelect);
componentMap.set('UploadFile', MtcnUploadFile);
componentMap.set('UploadImg', MtcnUploadImg);
componentMap.set('UploadImgSingle', MtcnUploadImgSingle);
componentMap.set('BillRule', MtcnInput);
componentMap.set('ModifyUser', MtcnInput);
componentMap.set('ModifyTime', MtcnInput);
componentMap.set('CreateUser', MtcnOpenData);
componentMap.set('CreateTime', MtcnOpenData);
componentMap.set('CurrOrganize', MtcnOpenData);
componentMap.set('CurrPosition', MtcnOpenData);
componentMap.set('RelationForm', MtcnRelationForm);
componentMap.set('RelationFormAttr', MtcnRelationFormAttr);
componentMap.set('PopupSelect', MtcnPopupSelect);
componentMap.set('PopupTableSelect', MtcnPopupTableSelect);
componentMap.set('PopupAttr', MtcnPopupAttr);
componentMap.set('NumberRange', MtcnNumberRange);
componentMap.set('Calculate', MtcnCalculate);
componentMap.set('InputTable', MtcnInputTable);
componentMap.set('Location', MtcnLocation);
componentMap.set('Iframe', MtcnIframe);
componentMap.set('CollegeProfessionalClass', MtcnCollegeProfessionalClass);
componentMap.set('Anchor', MtcnAnchor);

export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export { componentMap };
