.fullscreen-modal {
  overflow: hidden;

  .ant-modal {
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100%;
    max-width: 100%;

    &-content {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
      .ant-modal-body {
        flex: 1;
      }
      .ant-modal-header,
      .ant-modal-footer {
        flex-shrink: 0;
      }
    }
  }
}

.ant-modal {
  width: 600px;
  padding-bottom: 0;

  .ant-modal-body > .scrollbar {
    padding: 20px 50px 0;
    & > .scrollbar__wrap {
      margin-bottom: 0 !important;
    }
  }

  &-title {
    font-size: 16px;
    // font-weight: bold;

    .base-title {
      cursor: move !important;
    }
  }

  .ant-modal-body {
    padding: 0;

    > .scrollbar > .scrollbar__bar.is-horizontal {
      display: none;
    }
  }

  &-large {
    top: 60px;

    &--mini {
      top: 16px;
    }
  }

  &-header {
    height: 60px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .ant-modal-title {
      width: 100%;
    }
  }

  &-content {
    box-shadow: 0 4px 8px 0 rgb(0 0 0 / 20%), 0 6px 20px 0 rgb(0 0 0 / 19%);
  }

  & &-footer {
    button + button {
      margin-left: 10px;
    }
  }

  & &-close {
    font-weight: normal;
    outline: none;
    top: 0;
    right: 0;
    width: auto;
    height: auto;
  }

  &-close-x {
    display: inline-block;
    width: 96px !important;
    height: 60px !important;
    line-height: 60px !important;
  }

  &-confirm-body {
    .ant-modal-confirm-content {
      // color: #fff;

      > * {
        color: @text-color-help-dark;
      }
    }
  }

  &-confirm-confirm.error .ant-modal-confirm-body > .anticon {
    color: @error-color;
  }

  &-confirm-btns {
    .ant-btn:last-child {
      margin-right: 0;
    }
  }

  &-confirm-info {
    .ant-modal-confirm-body > .anticon {
      color: @warning-color;
    }
  }

  &-confirm-confirm.success {
    .ant-modal-confirm-body > .anticon {
      color: @success-color;
    }
  }
}

.ant-modal-confirm .ant-modal-body {
  padding: 24px !important;
}
@media screen and (max-height: 600px) {
  .ant-modal {
    top: 60px;
  }
}
@media screen and (max-height: 540px) {
  .ant-modal {
    top: 30px;
  }
}
@media screen and (max-height: 480px) {
  .ant-modal {
    top: 10px;
  }
}
