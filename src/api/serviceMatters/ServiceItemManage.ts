/*
 * @Description:事项管理
 * @Autor: Fhz
 * @Date: 2024-11-09 17:16:19
 * @LastEditors: Fhz
 * @LastEditTime: 2024-11-20 10:30:20
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  mtService = '/api/mtserviematters/mtServiceMattersInfo',
}
// 列表查询
export function getList(params: any) {
  return defHttp.get({ url: Api.mtService + `/getList`, params });
}
// 新增事项管理
export function addService(data: any) {
  return defHttp.post({ url: Api.mtService + `/addInfo`, data });
}

// 编辑事项管理
export function editService(id, data: any) {
  return defHttp.put({ url: Api.mtService + `/edit/${id}`, data });
}
// 启用服务事项
export function enableService(data: any) {
  return defHttp.post({ url: Api.mtService + `/enable`, data });
}
// 推荐服务事项
export function recommendService(data: any) {
  return defHttp.post({ url: Api.mtService + `/recommend`, data });
}
// 删除服务事项
export function delService(id) {
  return defHttp.delete({ url: Api.mtService + `/delete/${id}` });
}
// 查询服务事项表单
export function getServiceForm(id) {
  const params = { id };
  return defHttp.get({ url: `/api/mtserviematters/mtServiceMattersField/getAllList`, params });
}
// 查询应用列表
export function getAppList() {
  const params = { currentPage: 1, pageSize: 9999, status: 1 };
  return defHttp.get({ url: `/api/application/manage`, params });
}
// 查询数据字典
export function getDictData(id) {
  return defHttp.get({ url: `/api/system/DictionaryData/${id}/Data/Selector` });
}

// 查询服务事项-组件
export function getPortalServiceInfoList(params) {
  return defHttp.get({ url: `/api/mtserviematters/mtServiceMattersField/getPortalServiceInfoList`, params });
}

export function getPortalServiceInfoListBy(params) {
  return defHttp.get({ url: `/api/mtserviematters/mtServiceMattersField/getPortalServiceInfoListBy`, params });
}
