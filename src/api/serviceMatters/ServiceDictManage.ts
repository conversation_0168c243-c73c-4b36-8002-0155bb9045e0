/*
 * @Description:
 * @Autor: panmy
 * @Date: 2024-11-04 18:17:08
 * @LastEditors: Fhz
 * @LastEditTime: 2024-11-20 17:02:43
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  mtService = '/api/mtserviematters/mtServiceMattersDict',
}
//部门查询
export function getDeptDictList(params = {}) {
  return defHttp.get({ url: Api.mtService + `/queryDeptDictList`, params });
}
//部门添加
export function addDeptDict(data: any) {
  return defHttp.post({ url: Api.mtService + `/add`, data });
}
// 部门编辑
export function editDeptDict(data: any) {
  return defHttp.put({ url: Api.mtService + `/edit/${data.id}`, data });
}
// 删除部门
export function delDepDict(id) {
  return defHttp.delete({ url: Api.mtService + `/delete/${id}` });
}
// 主题查询
export function getThemeDictList(params = {}) {
  return defHttp.get({ url: Api.mtService + `/queryServiceThemeDictList`, params });
}
// 主题添加
export function addThemeDict(data) {
  return defHttp.post({ url: Api.mtService + `/addServiceItem`, data });
}
// id查询详情
export function fetchDictDetail(id) {
  return defHttp.get({ url: Api.mtService + `/queryById/${id}` });
}
