/*
 * @Description:
 * @Autor: panmy
 * @Date: 2024-07-04 18:28:34
 * @LastEditors: panmy
 * @LastEditTime: 2024-07-05 11:35:55
 */

import { defHttp } from '@/utils/http/axios';

// 【应用组件】-热门应用、推荐应用、在线应用
export function getCateList(data) {
  return defHttp.get({ url: '/api/visualdev/manager-car/cate-list', data });
}

// 【访问记录组件】-新增访问数
export function setVisitRecord(data) {
  return defHttp.post({ url: '/api/visualdev/manager-car/save-visit-record', data });
}
