/*
 * @Description: 个性化
 * @Autor: panmy
 * @Date: 2024-05-21 14:41:58
 * @LastEditors: panmy
 * @LastEditTime: 2024-05-31 16:38:14
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  application = '/api/low/code/personal/page',
}

// 获取个性化列表
export function getList(data) {
  return defHttp.get({ url: Api.application + '/getList/' + data.lowCodeAppId, data });
}
// 新建个性化
export function create(data) {
  return defHttp.post({ url: Api.application, data });
}
// 修改个性化
export function update(data) {
  return defHttp.post({ url: Api.application + '/update/' + data.id, data });
}
// 获取个性化
export function getInfo(id) {
  return defHttp.get({ url: Api.application + '/' + id });
}
// 删除个性化
export function delInfo(data) {
  return defHttp.post({ url: Api.application + '/delete/' + data.id, data });
}

// 获取个性化关联
export function getPersonalInfo(data) {
  return defHttp.get({ url: Api.application + '/getPersonalInfo', data });
}
