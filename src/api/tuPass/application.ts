/*
 * @Description: 应用
 * @Autor: panmy
 * @Date: 2024-05-21 14:41:58
 * @LastEditors: panmy
 * @LastEditTime: 2025-01-23 16:05:53
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  application = '/api/low/code/application',
}

// 获取应用列表
export function getSystemList(data) {
  return defHttp.get({ url: Api.application + '/list', data });
}
// 新建应用
export function create(data) {
  return defHttp.post({ url: Api.application, data });
}
// 修改应用
export function update(data) {
  return defHttp.post({ url: Api.application + '/update/' + data.id, data });
}
// 获取应用
export function getInfo(id) {
  return defHttp.get({ url: Api.application + '/' + id });
}

// 删除应用
export function delSystem(data) {
  return defHttp.post(
    { url: Api.application + '/delete/' + data.id, data },
    {
      isTransformResponse: false,
    },
  );
}
