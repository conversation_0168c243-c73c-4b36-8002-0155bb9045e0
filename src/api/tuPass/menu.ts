/*
 * @Description: 应用
 * @Autor: panmy
 * @Date: 2024-05-21 14:41:58
 * @LastEditors: panmy
 * @LastEditTime: 2025-01-24 11:40:21
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  tuPassPrefix = '/api/low/code/module',
}

// 发布
export function release(data) {
  return defHttp.post({ url: '/api/low/code/application/release', data });
}

// 取消发布
export function cancelRelease(data) {
  return defHttp.post(
    { url: '/api/low/code/application/cancelRelease', data },
    {
      isTransformResponse: false,
    },
  );
}

// 获取低代码菜单列表
export function getMenuList(data) {
  return defHttp.get({ url: Api.tuPassPrefix + '/getList/' + data.lowCodeAppId, data });
}

// 低代码--获取上级菜单下拉框
export function getMenuSelector(data) {
  return defHttp.get({ url: Api.tuPassPrefix + `/Selector/${!!data.id ? data.id : '0'}/${data.systemId}`, data });
}
// 低代码--新建菜单
export function create(data) {
  return defHttp.post({ url: Api.tuPassPrefix, data });
}
// 低代码--修改菜单
export function update(data) {
  return defHttp.put({ url: Api.tuPassPrefix + '/' + data.id, data });
}
// 低代码--获取菜单详情
export function getPermissionInfo(id) {
  return defHttp.get({ url: Api.tuPassPrefix + '/' + id });
}
// 低代码--删除菜单
export function delMenu(id) {
  return defHttp.delete({ url: Api.tuPassPrefix + '/' + id });
}
// 获取菜单详情
export function getInfo(id) {
  return defHttp.get({ url: Api.tuPassPrefix + '/' + id });
}

// 导出系统菜单数据
export function exportMenu(id) {
  return defHttp.get({ url: Api.tuPassPrefix + `/${id}/Actions/Export` });
}
