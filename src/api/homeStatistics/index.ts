/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-01-02 15:05:41
 * @LastEditors: panmy
 * @LastEditTime: 2025-01-02 17:24:19
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  homeAPi = '/api/homeStatistics/',
}

// 用户情况统计
export function getUserNumByType() {
  return defHttp.get({ url: Api.homeAPi + `getUserNumByType` });
}

// 当月用户登录信息
export function getMonthUserLoginNum(data) {
  return defHttp.get({ url: Api.homeAPi + `getMonthUserLoginNum/${data.size}` });
}

// 当月用户发送消息趋势
export function getMonthMessageNum(data) {
  return defHttp.get({ url: Api.homeAPi + `getMonthMessageNum/${data.size}` });
}

// 当月用户发起流程信息
export function getMonthFlowNum(data) {
  return defHttp.get({ url: Api.homeAPi + `getMonthFlowNum/${data.size}` });
}

// 获取消息信息统计
export function getMessageInfo() {
  return defHttp.get({ url: Api.homeAPi + `getMessageInfo` });
}
export function getDataSyncInfo() {
  return defHttp.get({ url: Api.homeAPi + `getDataSyncInfo` });
}

// 获取登录信息统计
export function getLoginInfo() {
  return defHttp.get({ url: Api.homeAPi + `getLoginInfo` });
}
// 获取登录信息统计???
export function getFlowCountInfo() {
  return defHttp.get({ url: Api.homeAPi + `getFlowCountInfo` });
}

// 获取数据同步信息统计
export function getdataInfo() {
  return defHttp.get({ url: Api.homeAPi + `getdataInfo` });
}

// 应用使用情况
export function getAppVisitNum() {
  return defHttp.get({ url: Api.homeAPi + `getAppVisitNum` });
}

// 获取应用信息统计
export function getAppCountInfo() {
  return defHttp.get({ url: Api.homeAPi + `getAppCountInfo` });
}

export function getMonitorAll() {
  return defHttp.get({ url: `/api/system/Monitor/getAll` });
}
