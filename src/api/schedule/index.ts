/*
 * @Description:
 * @Autor: panmy
 * @Date: 2024-06-24 16:29:05
 * @LastEditors: panmy
 * @LastEditTime: 2025-01-22 15:06:20
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/schedule/BaseScheduleClass/holiday',
  aggregationPrefix = '/api/schedule/BaseDataAggregation',
  BaseScheduleClassPrefix = '/api/schedule/BaseScheduleClass',
  datasourcePrefix = '/api/schedule/BaseScheduleClass/datasource',
  BaseNewsSitePerfix = '/api/schedule/BaseNewsSite',
  BaseInfoAggregationPerfix = '/api/schedule/BaseInfoAggregation',
  selfInfoPrefix = '/api/schedule/BaseDataAggregationPersonal',
}

// ---------- 日程分类
export function createBaseScheduleClass(data) {
  return defHttp.post({ url: Api.BaseScheduleClassPrefix, data });
}

export function updateBaseScheduleClass(data) {
  return defHttp.put({ url: Api.BaseScheduleClassPrefix + '/' + data.id, data });
}
export function updateStateBaseScheduleClass(data) {
  return defHttp.put({ url: Api.BaseScheduleClassPrefix + '/update-status/' + data.id + '/' + data.enabledMark });
}

export function delBaseScheduleClass(id) {
  return defHttp.delete({ url: Api.BaseScheduleClassPrefix + '/' + id });
}

export function getBaseScheduleClassInfo(id) {
  return defHttp.get({ url: Api.BaseScheduleClassPrefix + '/' + id });
}
export function getBaseScheduleClassList(data) {
  return defHttp.post({ url: Api.BaseScheduleClassPrefix + '/getList', data });
}

// ---------- 日程分类 假期
export function create(data) {
  return defHttp.post({ url: Api.Prefix, data });
}

export function update(data) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}

export function getInfo(id) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}

export function getHolidayList(data) {
  return defHttp.get({ url: Api.Prefix, data });
}

export function delScheduleQuery(id) {
  return defHttp.delete({ url: '/api/schedule/BaseScheduleClass/' + id });
}

export function copyYearData(data) {
  return defHttp.get({ url: Api.Prefix + '/cp', data });
}

// ---------- 个人数据聚合
export function createAggregation(data) {
  return defHttp.post({ url: Api.aggregationPrefix, data });
}
export function createreleaseAggregation(data) {
  return defHttp.post({ url: Api.aggregationPrefix + '/release', data });
}

export function updateAggregation(data) {
  return defHttp.put({ url: Api.aggregationPrefix + '/' + data.id, data });
}

export function getInfoAggregation(id) {
  return defHttp.get({ url: Api.aggregationPrefix + '/getInfoById/' + id });
}

export function getInfoAggregationList(data) {
  return defHttp.get({ url: Api.aggregationPrefix + '/getList', data });
}

export function delAggregation(id) {
  return defHttp.delete({ url: Api.aggregationPrefix + '/' + id });
}

export function getSelectorByTypeList() {
  return defHttp.get({ url: '/api/system/DataInterface/SelectorByType' });
}

// ---------- 日程
export function createDatasource(data) {
  return defHttp.post({ url: Api.datasourcePrefix, data });
}

export function updateDatasource(data) {
  return defHttp.put({ url: Api.datasourcePrefix + '/' + data.id, data });
}

export function delDatasource(id) {
  return defHttp.delete({ url: Api.datasourcePrefix + '/' + id });
}

export function getDatasourceInfo(id) {
  return defHttp.get({ url: Api.datasourcePrefix + '/' + id });
}
export function getDatasourceList(data) {
  return defHttp.get({ url: Api.datasourcePrefix, data });
}

// ------------ 资讯站点
export function createBaseNewsSite(data) {
  return defHttp.post({ url: Api.BaseNewsSitePerfix, data });
}

export function updateBaseNewsSite(data) {
  return defHttp.put({ url: Api.BaseNewsSitePerfix + '/' + data.id, data });
}

export function delBaseNewsSite(id) {
  return defHttp.delete({ url: Api.BaseNewsSitePerfix + '/' + id });
}

export function getBaseNewsSiteList(data) {
  return defHttp.get({ url: Api.BaseNewsSitePerfix });
}

// ------------ 资讯
export function createBaseInfoAggregation(data) {
  return defHttp.post({ url: Api.BaseInfoAggregationPerfix, data });
}

export function getBaseInfoAggregationInfo(data) {
  return defHttp.get({ url: Api.BaseInfoAggregationPerfix + '/getInfoById/' + data.id });
}

export function updateBaseInfoAggregation(data) {
  return defHttp.put({ url: Api.BaseInfoAggregationPerfix + '/' + data.id, data });
}
export function updateStatus(data) {
  return defHttp.post({ url: Api.BaseInfoAggregationPerfix + `/unable/${data.id}/${data.enabledMark}` });
}
export function delBaseInfoAggregation(id) {
  return defHttp.delete({ url: Api.BaseInfoAggregationPerfix + '/' + id });
}

export function getBaseInfoAggregationList(data) {
  return defHttp.get({ url: Api.BaseInfoAggregationPerfix + '/getList', data });
}

// 预览
export function getNewsPreview(data) {
  return defHttp.post({ url: '/api/schedule/BaseInfoAggregationContent/webCapture', data });
}

// 资讯列表
export function getNewsPreviewList(data) {
  return defHttp.get({ url: '/api/schedule/BaseInfoAggregationContent/getInfoContentList', data });
}

// 资讯抓取更新
export function updateContent(id) {
  return defHttp.put({ url: `/api/schedule/BaseInfoAggregation/updateContent/${id}` });
}

// 资讯设置订阅--自定组件
export function setNewsSubscribe(data) {
  return defHttp.post({ url: '/api/schedule/InfoColumnSubscribe/subscribe', data });
}

// 获取资讯可以订阅的栏目--自定组件
export function getSubscribeInfo(data) {
  return defHttp.get({ url: '/api/schedule/InfoColumnSubscribe/getSubscribeInfo', data });
}

// 获取用户已经订阅资讯栏目--自定组件
export function getPortalInfoList(data) {
  return defHttp.get({ url: '/api/schedule/BaseInfoAggregation/getPortalInfoList', data });
}
// 个人数据
export function getPersonalCardList() {
  return defHttp.get({ url: Api.selfInfoPrefix + '/getPersonalCardList' });
}

export function getPersonalCardInfo(data) {
  return defHttp.get({ url: Api.selfInfoPrefix + '/getPersonalCardInfo', data });
}

export function addMail(data) {
  return defHttp.post({ url: Api.selfInfoPrefix + '/addMail', data });
}

export function hideDisplayCard(data) {
  return defHttp.post({ url: Api.selfInfoPrefix + '/hideDisplayCard', data });
}

export function unbindingMail(userId) {
  return defHttp.put({ url: Api.selfInfoPrefix + '/unbindingMail/' + userId });
}
// export function fetchDetail(id) {
//   return defHttp.get({ url: '/api/system/DataInterface/unbindingMail/' + id });
// }
export function getCardInfo(id) {
  return defHttp.post({ url: `/api/system/DataInterface/${id}/Actions/Preview` }, { isReturnNativeResponse: true });
}

export function getUser() {
  return defHttp.get({ url: '/api/oauth/CurrentUser' });
}
