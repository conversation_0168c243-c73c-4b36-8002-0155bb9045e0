/*
 * @Description:
 * @Autor: panmy
 * @Date: 2024-07-04 15:30:04
 * @LastEditors: Fhz
 * @LastEditTime: 2025-01-10 17:01:08
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/schedule/BaseCardInfo',
}

// 获取卡片数据详情
export function getBaseCardById(id) {
  return defHttp.get({ url: Api.Prefix + `/getInfoById/` + id });
}
// 新建卡片
export function createBaseCard(data) {
  return defHttp.post({ url: Api.Prefix, data });
}
// 修改卡片
export function updateBaseCard(data) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}
// 删除卡片
export function delBaseCard(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

export function getBaseCardByList(data) {
  return defHttp.get({ url: Api.Prefix + `/getList`, data });
}
// 查询卡片归属
export function getBelongList() {
  return defHttp.get({ url: Api.Prefix + `/getBelongList` });
}
