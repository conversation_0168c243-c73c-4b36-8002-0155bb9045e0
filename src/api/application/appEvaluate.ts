import { defHttp } from '@/utils/http/axios';
enum Api {
  Prefix = '/api/application/manage',
  evaluateService = '/api/application/appraise',
}
// 获取应用评价列表
export function getSystemList(data) {
  return defHttp.get({ url: `${Api.Prefix}`, data });
}
// 获取应用评价详情列表
export function getEvaluateList(data) {
  return defHttp.get({ url: `${Api.evaluateService}/getCommentsList`, data });
}
// 删除应用评价
export function deleteEvaluate(id) {
  return defHttp.delete({ url: `${Api.evaluateService}/${id}` });
}
