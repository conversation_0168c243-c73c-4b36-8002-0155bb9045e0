/*
 * @Description:
 * @Autor: panmy
 * @Date: 2024-04-10 14:17:28
 * @LastEditors: Fhz
 * @LastEditTime: 2025-01-15 17:31:54
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/application/collect',
  PrefixManage = '/api/application/manage',
}

// 获取应用列表
export function getSystemList(data) {
  return defHttp.get({ url: '/api/application/manage', data });
}
// 通过服务部门或服务角色获取应用列表
export function getSystemListByPartOrRole(data) {
  return defHttp.get({ url: `${Api.PrefixManage}/getServerAppList`, data });
}
// 获取近5次的上下线时间记录
export function getRecentVisitLog(id) {
  return defHttp.get({ url: `${Api.PrefixManage}/getRecentVisitLog/${id}` });
}
// 新建应用
export function create(data) {
  return defHttp.post({ url: '/api/application/manage', data });
}
// 修改应用
export function update(data) {
  return defHttp.put({ url: '/api/application/manage/' + data.id, data });
}
// 获取应用
export function getInfo(id) {
  return defHttp.get({ url: '/api/application/manage/' + id });
}
// 删除应用
export function delSystem(id) {
  return defHttp.delete({ url: '/api/application/manage/' + id });
}

//根据配置类型获取当前数据
export function getConfigTypeInfo(configType) {
  return defHttp.get({ url: '/api/application/manage/config/list-config-type/' + configType });
}

//根据配置类型获取当前数据
export function getConfigTypeCreate(data) {
  return defHttp.post({ url: '/api/application/manage/config/create', data });
}

// 修改应用
export function configTypeUpdate(data) {
  return defHttp.put({ url: '/api/application/manage/config/' + data.id, data });
}

// 删除应用
export function delConfigType(id) {
  return defHttp.delete({ url: '/api/application/manage/config/' + id });
}

// 根据父节点ID获取当前子节点数据
export function getConfigIdInfo(data) {
  return defHttp.get({ url: '/api/application/manage/config/list-config-id/' + data.id, data });
}

// 查询应用关系
export function getManageAuthorize(data) {
  return defHttp.get({ url: '/api/application/manage/get-manage-authorize/' + data.id, data });
}
// 更新应用关系
export function updateManageAuthorize(id, params) {
  return defHttp.put({ url: '/api/application/manage/update-manage-authorize/' + id, params });
}
// 删除应用关系
export function deleteManageAuthorize(id) {
  return defHttp.delete({ url: '/api/application/manage/delete-manage-authorize/' + id });
}

// 点击收藏当前应用
export function setUserCollect(data) {
  return defHttp.post({ url: Api.Prefix, data });
}

// 应用组件----自定义组件
export function getManageManggetCarListNew(data) {
  return defHttp.get({ url: '/api/visualdev/manager-car/cate-list-new', data });
}
// 【访问记录组件】-新增访问数----自定义组件
export function saveVisitRecord(data) {
  return defHttp.post({ url: '/api/visualdev/manager-car/save-visit-record', data });
}

// 收藏夹列表
export function getUserBookMark(data) {
  return defHttp.get({ url: `/api/application/collect/${data.userId}` });
}

// 新建收藏夹
export function saveBookMark(data) {
  return defHttp.post({ url: '/api/application/collect/saveBookMark', data });
}

// 修改收藏夹信息
export function updateBookMark(data) {
  return defHttp.put({ url: `/api/application/collect/${data.id}`, data });
}

// 删除收藏夹
export function delBookMark(id) {
  return defHttp.delete({ url: `/api/application/collect/${id}` });
}

// 收藏夹排序
export function sortBookMark(data) {
  return defHttp.put({ url: `/api/application/collect/moveBookmark`, data });
}

export function sortCollectApp(data) {
  return defHttp.put({ url: `/api/application/collect/sortCollectApp`, data });
}

// 应用收藏
export function saveAppToBookMark(data) {
  return defHttp.post({ url: '/api/application/collect', data });
}
// 取消收藏
export function rejectCollect(data) {
  return defHttp.post({ url: '/api/application/collect/rejectCollect', data });
}

// 移动收藏夹下应用
export function moveCollectApp(data) {
  return defHttp.put({ url: '/api/application/collect/moveCollectApp', data });
}

// 获取当前用户收藏应用列表
export function getUserCollect(userId) {
  return defHttp.get({ url: Api.Prefix + `/${userId}` }, { isTransformResponse: false });
}
