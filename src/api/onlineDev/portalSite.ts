import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/base/site',
  plan = '/api/application/site/plan',
  sitePrefix = '/api/base/site',
  DashboardPrefix = '/api/visualdev/Dashboard',
  SchedulePrefix = '/api/system/Schedule',
  portalPrefix = '/api/system/PortalManage',
  planMenuPrefix = '/api/application/site/plan/menu',
}

// 获取站点模板列表
export function getPortalList(data) {
  return defHttp.get({ url: '/api/base/site/template', data });
}
// 删除站点模板
export function delPortal(id) {
  return defHttp.delete({ url: `/api/base/site/template/${id}` });
}
// 修改站点模板
export function updatePortal(data) {
  return defHttp.put({ url: `/api/base/site/template/${data.id}`, data });
}
// 获取站点模板信息
export function getInfo(id) {
  return defHttp.get({ url: `/api/base/site/template/${id}` });
}

// 获取站点列表
export function getSiteList(data) {
  return defHttp.get({ url: Api.sitePrefix, data });
}
// 删除站点
export function delSite(id) {
  return defHttp.delete({ url: Api.sitePrefix + `/${id}` });
}
// 新建站点
export function createSite(data) {
  return defHttp.post({ url: Api.sitePrefix, data });
}
// 修改站点
export function updateSite(data) {
  return defHttp.put({ url: Api.sitePrefix + `/${data.id}`, data });
}
// 获取站点信息
export function getSiteInfo(id) {
  return defHttp.get({ url: Api.sitePrefix + `/${id}` });
}

// 新建门户
export function createPortal(data) {
  return defHttp.post({ url: '/api/base/site/template', data });
}
// 复制门户
export function copyPortal(id) {
  return defHttp.post({ url: Api.Prefix + `/${id}/Actions/Copy` });
}
// 导出门户
export function exportPortal(id) {
  return defHttp.post({ url: Api.Prefix + `/${id}/Actions/ExportData` });
}
// 获取门户下拉框列表
export function getPortalSelector(type?) {
  return defHttp.get({ url: Api.Prefix + '/Selector?platform=Web', data: { type } });
}
// 切换用户门户默认显示
export function setDefaultPortal(id) {
  return defHttp.put({ url: Api.Prefix + `/${id}/Actions/SetDefault?platform=Web` });
}
// 获取门户展示数据(权限)
export function getAuthPortal(id, data) {
  return defHttp.get({ url: Api.Prefix + `/${id}/auth`, data });
}
// 获取全国省市区
export function getAtlas() {
  return defHttp.get({ url: Api.AtlasPrefix });
}
// 获取地图json
export function getMapData(data) {
  return defHttp.get({ url: Api.AtlasPrefix + '/geojson', data });
}
// 用户拖拽后更新
export function UpdateCustomPortal(id, data) {
  return defHttp.put({ url: Api.Prefix + `/Custom/Save/${id}`, data });
}
// 获取我的待办
export function getFlowTodoCount(data) {
  return defHttp.post({ url: Api.DashboardPrefix + '/FlowTodoCount', data });
}
// 获取通知公告
export function getNoticeList(data) {
  return defHttp.post({ url: Api.DashboardPrefix + '/Notice', data });
}
// 获取未读邮件
export function getEmailList() {
  return defHttp.get({ url: Api.DashboardPrefix + '/Email' });
}
// 获取待办事项
export function getFlowTodoList() {
  return defHttp.get({ url: Api.DashboardPrefix + '/FlowTodo' });
}
// 获取我的待办事项
export function getMyFlowTodoList(data) {
  return defHttp.get({ url: Api.DashboardPrefix + '/MyFlowTodo', data });
}
// 获取日程安排列表
export function getScheduleList(data) {
  return defHttp.get({ url: Api.SchedulePrefix, data });
}
// 新建日程安排
export function createSchedule(data) {
  return defHttp.post({ url: Api.SchedulePrefix, data });
}
// 更新日程安排
export function updateSchedule(data, type) {
  return defHttp.put({ url: Api.SchedulePrefix + `/${data.id}/${type}`, data });
}
// 删除日程安排
export function delSchedule(id, type) {
  return defHttp.delete({ url: Api.SchedulePrefix + `/${id}/${type}` });
}
// 获取日程安排信息
export function getScheduleInfo(id) {
  return defHttp.get({ url: Api.SchedulePrefix + `/${id}` });
}
// 查看日程详情
export function getScheduleDetail(groupId, id) {
  return defHttp.get({ url: Api.SchedulePrefix + `/detail?groupId=${groupId}&id=${id}` });
}
//发布
export function release(id, data) {
  return defHttp.put({ url: Api.Prefix + `/Actions/release/${id}`, data });
}
// 获取门户管理列表
export function getPortalManageList(data) {
  return defHttp.get({ url: Api.portalPrefix + `/list/${data.systemId}`, data });
}
// 获取门户名称列表
export function getPortalManageSelector(data) {
  return defHttp.get({ url: Api.Prefix + `/manage/Selector/${data.systemId}`, data });
}
// 获取门户管理详情
export function getPortalManageInfo(id) {
  return defHttp.get({ url: Api.portalPrefix + `/${id}` });
}
// 新建门户管理
export function createPortalManage(data) {
  return defHttp.post({ url: Api.portalPrefix, data });
}
// 编辑门户管理
export function updatePortalManage(data) {
  return defHttp.put({ url: Api.portalPrefix + `/${data.id}`, data });
}
// 删除门户管理
export function delPortalManage(id) {
  return defHttp.delete({ url: Api.portalPrefix + `/${id}` });
}
// 根据方案id查询计页面列表
export function fetchDesignListBySchemeId(id) {
  return defHttp.get({ url: Api.Prefix + `/page/getPage/${id}` });
}
// 删除询计页面
export function delDesignPage(id) {
  return defHttp.delete({ url: Api.Prefix + `/page/${id}` });
}
// 获取设计页面详情
export function fetchDesignPage(id) {
  return defHttp.get({ url: Api.Prefix + `/page/${id}` });
}
// 根据方案id查询当前关联模板的子模版列表
export function fetchTemplateListBySchemeId(id) {
  return defHttp.get({ url: Api.plan + `/getTemplateLayout/${id}` });
}

// 新建设计页面
export function createDesignPage(data) {
  return defHttp.post({ url: Api.Prefix + `/page`, data });
}
// 编辑设计页面
export function updateDesignPage(data) {
  return defHttp.put({ url: Api.Prefix + `/page/${data.id}`, data });
}
// 获取展示方案列表
export function getPlanList(data) {
  return defHttp.get({ url: Api.plan + `/getListBy/${data.id}` });
}
// 删除展示方案
export function delPlan(id) {
  return defHttp.delete({ url: Api.plan + `/${id}` });
}
// 新建展示方案
export function createPlan(data) {
  return defHttp.post({ url: Api.plan, data });
}
// 修改展示方案
export function updatePlan(data) {
  return defHttp.put({ url: Api.plan + `/${data.id}`, data });
}
// 修改展示方案状态
export function updatePlanStatus(data) {
  return defHttp.post({ url: Api.plan + `/unable`, data });
}
// 获取展示方案信息
export function getPlanInfo(id) {
  return defHttp.get({ url: Api.plan + `/${id}` });
}
// 获取展示方案信息菜单
export function copyPlan(data) {
  return defHttp.post({ url: Api.plan + '/copySitePlan', data });
}

// 新建全局设置管理菜单
export function createPlanMenuManage(data) {
  return defHttp.post({ url: Api.planMenuPrefix, data });
}
// 编辑全局设置管理菜单
export function updatePlanMenuManage(data) {
  return defHttp.put({ url: Api.planMenuPrefix + `/${data.id}`, data });
}
// 删除全局设置管理菜单
export function delPlanMenuManage(id) {
  return defHttp.delete({ url: Api.planMenuPrefix + `/${id}` });
}

// 查询站点方案菜单信息
export function getPlanMenuList(id) {
  return defHttp.get({ url: Api.planMenuPrefix + `/getBaseInfo/${id}` });
}

// 获取全局设置管理菜单详情
export function getPlanMenuInfo(id) {
  return defHttp.get({ url: Api.planMenuPrefix + `/${id}` });
}

// 获取展示方案信息菜单
export function setMenuUnable(data) {
  return defHttp.post({ url: Api.planMenuPrefix + '/unable/' + data.id, data });
}
// 获取展示方案信息
export function getAllDesignPage(id) {
  return defHttp.get({ url: `/api/base/site/page/getPage/${id}` });
}
