/*
 * @Description:系统发布
 * @Autor: Fhz
 * @Date: 2025-01-10 09:47:20
 * @LastEditors: Fhz
 * @LastEditTime: 2025-01-10 09:56:58
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/notice/version/Notice',
}

// 获取系统发布列表
export function getNoticeList(data) {
  return defHttp.post({ url: Api.Prefix + '/List', data });
}
// 新建发布
export function create(data) {
  return defHttp.post({ url: Api.Prefix, data });
}
// 修改发布
export function update(data) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}
// 获取发布详情
export function getInfo(id) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}
// 删除发布
export function delNotice(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}
// 发布
export function release(id) {
  return defHttp.put({ url: Api.Prefix + `/${id}/Actions/Release` });
}
