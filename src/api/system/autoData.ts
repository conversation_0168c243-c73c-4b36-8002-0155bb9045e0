/*
 * @Description: 自动化任务
 * @Autor: panmy
 * @Date: 2025-01-14 15:19:09
 * @LastEditors: panmy
 * @LastEditTime: 2025-01-21 09:09:57
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/system/autoData',
}

// 新增同步任务
export function saveAutoData(data) {
  return defHttp.post({ url: Api.Prefix, data });
}

// 新增同步任务
export function updateAutoData(data) {
  return defHttp.put({ url: Api.Prefix + `/${data.id}`, data });
}

// 查询同步任务信息列表
export function getAutoDataList(data) {
  return defHttp.get({ url: Api.Prefix + '/list', data });
}

// 同步
export function setAutoDataSys(id) {
  return defHttp.post({ url: Api.Prefix + `/sys-data/${id}` });
}

// 单条同步
export function setAutoDataSysFail(id) {
  return defHttp.post({ url: Api.Prefix + `/sys-data-fail/${id}` });
}

// 根据ID删除同步任务
export function delAutoDataTask(id) {
  return defHttp.delete({ url: Api.Prefix + `/${id}` });
}

// 根据ID查询同步任务详情
export function getAutoDataTaskDetail(id) {
  return defHttp.get({ url: Api.Prefix + `/${id}` });
}

export function getListDetail(id) {
  return defHttp.get({ url: Api.Prefix + `/list-detail` });
}
