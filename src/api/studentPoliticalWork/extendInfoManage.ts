/*
 * @Description:扩展信息管理
 * @Autor: Fhz
 * @Date: 2025-02-13 09:50:21
 * @LastEditors: Fhz
 * @LastEditTime: 2025-03-24 20:22:05
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  prefix = '/api/business/zg/sz',
}
//获取政工人员扩展信息设置列表（不分页）
export function getListAll(data) {
  return defHttp.get({ url: Api.prefix + `/listAll`, data });
}
//获取政工人员扩展信息设置列表（不分页）- 包含统计数量字段
export function getListAllTototal(data) {
  return defHttp.get({ url: Api.prefix + `/listAllTototal` });
}
// 编辑扩展信息
export function editExtend(data) {
  return defHttp.put({ url: Api.prefix + `/edit/${data.id}`, data });
}
// 删除扩展信息
export function delExtend(id) {
  return defHttp.delete({ url: Api.prefix + `/remove/${id}` });
}
// 查看扩展信息详情
export function getExtendDetail(id) {
  return defHttp.get({ url: Api.prefix + `/${id}` });
}

// 扩展信息详情

// 查询扩展信息列表-分页
export function getExtendDetailList(data) {
  return defHttp.get({ url: `/api/${data.path}/getList`, data });
}
// 查询扩展信息列表-不分页
export function getExtendListNoPagination(data) {
  return defHttp.get({ url: `/api/${data.path}/listAll`, data });
}
// 新增扩展详情信息
export function saveExtendDetailInfo(data) {
  return defHttp.post({ url: `/api/${data.path}/save`, data });
}
// 批量新增扩展详情信息
export function batchSaveExtendInfo(data) {
  return defHttp.post({ url: `/api/${data.path}/batchSave`, data: data.values });
}
// 修改扩展详情信息
export function editExtendDetailInfo(data) {
  return defHttp.put({ url: `/api/${data.path}/edit/${data.id}`, data });
}
// 查询详情
export function getExtendDetailInfo(data) {
  return defHttp.get({ url: `/api/${data.path}/${data.id}` });
}
// 删除扩展详情信息
export function delExtendDetailInfo(data) {
  return defHttp.delete({ url: `/api/${data.path}/remove/${data.id}` });
}
