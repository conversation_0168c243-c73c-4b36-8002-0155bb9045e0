/*
 * @Description:学生管理队伍
 * @Autor: Fhz
 * @Date: 2025-02-13 09:50:21
 * @LastEditors: Fhz
 * @LastEditTime: 2025-03-24 17:55:50
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  teacherPrefix = '/api/business/zg/ry', //学生管理队伍
  employmentPrefix = '/api/business/zg/rz', //任职信息
  categoryPrefix = '/api/business/zg/rzlb', //任职类别
}

// 学生管理队伍

// 查询学生管理队伍列表
export function getTeacherList(data) {
  return defHttp.get({ url: Api.teacherPrefix + `/getList`, data });
}
//保存学生管理队伍信息
export function saveTeacher(data) {
  return defHttp.post({ url: Api.teacherPrefix + `/save`, data });
}
// 编辑学生管理队伍信息
export function updateTeacher(data) {
  return defHttp.put({ url: Api.teacherPrefix + `/edit/${data.id}`, data });
}
//删除学生管理队伍信息
export function delTeacher(id) {
  return defHttp.delete({
    url:
      Api.teacherPrefix +
      `/remove/
    ${id}`,
  });
}
// 批量删除学生管理队伍信息
export function delTeacherBatch(data) {
  return defHttp.delete({ url: Api.teacherPrefix + `/batchRemove`, params: data });
}
//查询学生管理队伍详情
export function getTeacherDetail(zgh) {
  return defHttp.get({ url: Api.teacherPrefix + `/byzgh/${zgh}` });
}

// 任职信息

// 查询任职信息列表-分页
export function getEmploymentList(data) {
  return defHttp.get({ url: Api.employmentPrefix + `/getList`, data });
}
// 查询任职信息列表-不分页
export function getEmploymentListAll(data) {
  return defHttp.get({ url: Api.employmentPrefix + `/listAll`, data });
}
// 保存任职信息
export function saveEmployment(data) {
  return defHttp.post({ url: Api.employmentPrefix + `/save`, data });
}
// 编辑任职信息
export function updateEmployment(data) {
  return defHttp.put({ url: Api.employmentPrefix + `/edit/${data.id}`, data });
}
// 批量编辑任职信息
export function updateEmploymentBatch(data) {
  return defHttp.put({ url: Api.employmentPrefix + `/editBatch`, data });
}
//批量删除任职信息
export function delEmployment(data) {
  return defHttp.delete({
    url: Api.employmentPrefix + `/removeBatch`,
    params: data,
  });
}
//查询任职信息详情
export function getEmploymentDetail(id) {
  return defHttp.get({ url: Api.employmentPrefix + `/${id}` });
}

// 任职类别

// 查询任职类别列表（不分页）
export function getCategoryList(data) {
  return defHttp.get({ url: Api.categoryPrefix + `/listAll`, data });
}
// 保存任职类别
export function saveCategory(data) {
  return defHttp.post({ url: Api.categoryPrefix + `/save`, data });
}
// 编辑任职类别
export function updateCategory(data) {
  return defHttp.put({ url: Api.categoryPrefix + `/edit/${data.id}`, data });
}
//删除任职类别
export function delCategory(id) {
  return defHttp.delete({
    url:
      Api.categoryPrefix +
      `/remove/
    ${id}`,
  });
}
//查询任职类别详情
export function getCategoryDetail(id) {
  return defHttp.get({ url: Api.categoryPrefix + `/${id}` });
}
