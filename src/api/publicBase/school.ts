/*
 * @Description: 学习类
 * @Autor: panmy
 * @Date: 2025-03-19 14:26:53
 * @LastEditors: panmy
 * @LastEditTime: 2025-04-22 15:03:41
 */

import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/schedule/BaseCardInfo',
}

// 获取院系,专业,班级 , 院系-专业 , 专业-班级, 院系-专业-班级 级联数据
export function getShoolStructureTypeList(data) {
  return defHttp.get({ url: `/api/public-base/cascade/cascade`, data });
}

// 获取字段信息列表
export function getFieldList(data) {
  return defHttp.get({ url: `/api/public-base/jbxx/zd/getList`, data });
}

// 查询统计
export function getStatisticsByFields(data) {
  return defHttp.post({ url: `/api/public-base/jbxx/zgry/statistics-by-fields`, data });
}

// 统计明细
export function getDynamicDetails(data) {
  return defHttp.post({ url: `/api/public-base/jbxx/zgry/dynamic-details`, data });
}

// 统计表
export function getDynamicStatistics(data) {
  return defHttp.post({ url: `/api/public-base/jbxx/zgry/dynamic-statistics`, data });
}
