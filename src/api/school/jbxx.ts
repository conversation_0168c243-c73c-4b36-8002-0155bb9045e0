/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-02-28 13:55:40
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-07 16:21:09
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  jbxx = '/api/public-base/jbxx',
}

// 质量字段填写分析统计
export function countFieldAnalyse(data: any) {
  return defHttp.get({ url: Api.jbxx + '/countFieldAnalyse', data });
}

// 根据院系代码查询对应字段的填写统计
export function countDwFieldNum(data: any) {
  return defHttp.get({ url: Api.jbxx + '/countDwFieldNum', data });
}

// 当前学生基本信息列表
export function studentAllList(data: any) {
  return defHttp.get({ url: Api.jbxx + '/all-list', data });
}

export function getXsInfoCount(data: any) {
  return defHttp.get({ url: Api.jbxx + '/getXsInfoCount', data });
}
