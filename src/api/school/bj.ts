/*
 * @Description: 班级管理
 * @Autor: panmy
 * @Date: 2025-02-24 10:47:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-07 15:46:42
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  bj = '/api/public-base/bj',
}

// 新增班级信息
export function addClass(data: any) {
  return defHttp.post({ url: Api.bj + '/saveBjInfo', data });
}

// 编辑班级信息
export function updateBjInfo(data: any) {
  return defHttp.put({ url: Api.bj + `/editBjInfo/${data.id}`, data });
}
// 删除班级
export function delBjInfo(data: any) {
  return defHttp.delete({ url: Api.bj + `/deleteBjInfo/${data.bjdm}`, data });
}

export function getBjInfo(data: any) {
  return defHttp.get({ url: Api.bj + `/getInfo/${data.bjdm}` });
}

// 查询学校-院系-学年-班级树结构
export function getDwXnBjTreeList(data: any) {
  return defHttp.get({ url: Api.bj + '/getDwXnBjTreeList', data });
}

// 根据类型（未分班，待认定，已分班）查询当前学生列表
export function getStuInfoList(data: any) {
  return defHttp.get({ url: Api.bj + '/getStuInfoList', data }, { isReturnNativeResponse: true });
}

// 查询分班人数统计
export function getDivideTypeCount(data: any) {
  return defHttp.get({ url: Api.bj + '/getDivideTypeCount', data });
}

// 调整学生学籍状态
export function changeStatus(data: any) {
  return defHttp.post({ url: Api.bj + '/changeStatus', data });
}

// 调整学生学籍状态
export function divideStuClass(data: any) {
  return defHttp.post({ url: Api.bj + '/divideStuClass', data });
}

// 班级统计图
export function getBjInfoCount(data: any) {
  return defHttp.get({ url: Api.bj + '/getBjInfoCount', data });
}

// 班级明细列表
export function getBjInfoList(data: any) {
  return defHttp.get({ url: Api.bj + '/getBjInfoList', data });
}

// 根据班级查询带班信息
export function getBjLeaderInfoList(data: any) {
  return defHttp.get({ url: `/api/public-base/zgrz/getBjLeaderInfoList/${data.bjdm}`, data });
}
// 根据班级获取当前班干部列表信息
export function getXsgbListBy(data: any) {
  return defHttp.get({ url: `/api/public-base/xsgb/getXsgbListBy/${data.bjdm}`, data });
}

export function getBjLevelRzInfo(data: any) {
  return defHttp.get({ url: `/api/public-base/zgrz/getBjLevelRzInfo`, data });
}
// 给当前班级聘任老师
export function saveRzInfo(data: any) {
  return defHttp.post({ url: `/api/public-base/zgrz/saveRzInfo`, data });
}
// 当前班级解聘离任老师
export function removeRzInfo(data: any) {
  return defHttp.put({ url: `/api/public-base/zgrz/removeRzInfo`, data });
}

// 当前班级解聘离任老师
export function getJzgDetail(data: any) {
  return defHttp.get({ url: `/api/public-base/zgrz/${data.id}`, data });
}

export function getHistoryRzInfoList(data: any) {
  return defHttp.get({ url: `/api/public-base/zgrz/getHistoryRzInfoList/${data.zgh}`, data });
}

// 保存学生干部信息
export function saveXsgb(data: any) {
  return defHttp.post({ url: `/api/public-base/xsgb/save`, data });
}
// 编辑学生干部信息
export function editXsgb(data: any) {
  return defHttp.put({ url: `/api/public-base/xsgb/edit/${data.id}`, data });
}
export function removeBgb(data: any) {
  return defHttp.delete({ url: `/api/public-base/xsgb/remove/${data.id}`, data });
}

// 根据班级获取当前班级学生信息列表
export function getStuListBy(data: any) {
  return defHttp.get({ url: `/api/public-base/jbxx/getStuListBy`, data });
}

// 查询已推送名单
export function getPushedStuInfoList(data: any) {
  return defHttp.get({ url: `/api/public-base/bj/getPushedStuInfoList`, data });
}

// 撤销分班
export function quashChangeStuClass(data: any) {
  return defHttp.delete({ url: `/api/public-base/bj/quashChangeStuClass`, data });
}

// 同意、拒绝分班
export function auditChangeStuClass(data: any) {
  return defHttp.delete({ url: `/api/public-base/bj/auditChangeStuClass`, data });
}

// 查询班级男女比例
export function getSexAccount(data: any) {
  return defHttp.get({ url: `/api/public-base/jbxx/getMaleToFemaleRatio/${data.bjdm}`, data });
}
