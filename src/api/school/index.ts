/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-02-26 14:55:22
 * @LastEditors: Fhz
 * @LastEditTime: 2025-03-07 11:14:35
 */
import { defHttp } from '@/utils/http/axios';

// 查询学院
export function getXY(data) {
  return defHttp.get({ url: `/api/public-base/bj/getDwInfoList`, data });
}

// 根据学院查专业
export function getZY(data: { dwdm: string }) {
  return defHttp.get({ url: `/api/public-base/zy/getZyInfoList`, data });
}

// 根据学院 查年级
export function getCollegeGradeInfo(data) {
  return defHttp.get({ url: `/api/public-base/bj/getNjListByDw`, data });
}

// 根据学院、专业 查年级
export function getNJ(data: { dwdm: string; zydm: string }) {
  return defHttp.get({ url: `/api/public-base/bj/getNjList/${data.dwdm}/${data.zydm}` });
}

// 根据通过院系+专业+年级，查询班级列表
export function getBJ(data: { dwdm: string; zydm: string; nj: string }) {
  return defHttp.get({ url: `/api/public-base/bj/getBjList`, data });
}

// 获取教职工列表
export function getJzgList(data) {
  return defHttp.get({ url: `/api/public-base/zgrz/getJzgList`, data });
}

// 根据条件查询学生列表
export function getStuListBy(data) {
  return defHttp.get({ url: `/api/public-base/jbxx/getStuListBy`, data });
}
// 根据当前登录人获取批次信息
export function getUserPcsz(xsbh) {
  return defHttp.get({ url: `/api/public-base/jbxx/pcsz/getUserPcsz/${xsbh}` });
}
