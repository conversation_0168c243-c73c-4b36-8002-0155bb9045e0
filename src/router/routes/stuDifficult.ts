/*
 * @Description:困难生
 * @Autor: fhz
 * @Date: 2024-12-13 13:46:47
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-23 16:31:10
 */
import type { AppRouteRecordRaw } from '@/router/types';
export const stuDifficultRoutes: AppRouteRecordRaw = [
  {
    path: '/Questionnaire',
    component: () => import('@/views/stuDifficult/components/Questionnaire.vue'),
    name: 'Questionnaire',
    meta: {
      title: '问卷填写',
      defaultTitle: '问卷填写',
      icon: 'icon-ym icon-ym-portal-schedule',
    },
  },
];
