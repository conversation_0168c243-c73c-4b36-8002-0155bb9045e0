<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button preIcon="icon-ym icon-ym-btn-download" type="primary" @click="handleExport">导出</a-button>
          </template>
        </BasicTable>
      </div>
    </div>
    <ExportModal @register="registerExportModal" @download="handleDownload" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, BasicColumn, TableAction, ActionItem } from '@/components/Table';
import ExportModal from '@/components/CommonModal/src/ExportModal.vue';

const { createMessage } = useMessage();

const columns: BasicColumn[] = [
  { title: '学号', dataIndex: 'studentNo', width: 100, resizable: true, ellipsis: true,fixed: 'left' },
  { title: '姓名', dataIndex: 'name', width: 100, resizable: true, ellipsis: true ,fixed: 'left' },
  { title: '院系', dataIndex: 'college', width: 180, resizable: true, ellipsis: true },
  { title: '专业', dataIndex: 'major', width: 140, resizable: true, ellipsis: true },
  { title: '申请时间', dataIndex: 'applyDate', width: 150, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '申请描述', dataIndex: 'applyReason', width: 160, resizable: true, ellipsis: true },
  { title: '评定学年', dataIndex: 'year', width: 120, resizable: true, ellipsis: true },
  { title: '评定学期', dataIndex: 'term', width: 100, resizable: true, ellipsis: true },
  { title: '申请困难等级', dataIndex: 'applyType', width: 120, resizable: true, ellipsis: true },
  { title: '评定困难等级', dataIndex: 'difficultType', width: 120, resizable: true, ellipsis: true },
  { title: '性别', dataIndex: 'gender', width: 60, resizable: true, ellipsis: true },
  { title: '出生日期', dataIndex: 'birthday', width: 120, resizable: true, ellipsis: true },
  { title: '血型', dataIndex: 'bloodType', width: 80, resizable: true, ellipsis: true },
  { title: '民族', dataIndex: 'nation', width: 80, resizable: true, ellipsis: true },
  { title: '身份证件类型', dataIndex: 'idType', width: 120, resizable: true, ellipsis: true },
  { title: '身份证号', dataIndex: 'idNo', width: 180, resizable: true, ellipsis: true },
];

const formConfig = {
  schemas: [
    {
      field: 'keyword',
      label: '',
      component: 'Input',
      componentProps: {
        placeholder: '请输入学号/姓名',
      },
    },
    {
      field: 'year',
      label: '',
      component: 'Select',
      componentProps: {
        placeholder: '请选择学年',
        options: [
          { fullName: '2023-2024学年', value: '2023-2024学年' },
          { fullName: '2022-2023学年', value: '2022-2023学年' },
          { fullName: '2021-2022学年', value: '2021-2022学年' },
          { fullName: '2020-2021学年', value: '2020-2021学年' },
          { fullName: '2019-2020学年', value: '2019-2020学年' },
        ],
      },
    },
  ],
};

const [registerTable, { reload }] = useTable({
  // api: getDifficultStudentList,
  dataSource: [
      {
        id: 1,
        studentNo: 'xs1801',
        name: '苏洋',
        college: '现代工程与应用科学学院',
        major: '材料科学与工程',
        applyDate: '2024-08-06 00:00:00',
        applyReason: '学生综合素质低',
        year: '2024-2025学年',
        term: '不分学期',
        applyType: '特别困难',
        difficultType: '特别困难',
        gender: '男',
        birthday: '1994-06-08',
        bloodType: '未知血型',
        nation: '蒙古族',
        idType: '居民身份证',
        idNo: '430204...',
      },
      {
        id: 2,
        studentNo: '1832201',
        name: '胡娟',
        college: '历史学院',
        major: '历史学',
        applyDate: '2024-05-07 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '特别困难',
        difficultType: '特别困难',
        gender: '男',
        birthday: '1999-07-31',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 3,
        studentNo: '1832202',
        name: '张冬怡',
        college: '历史学院',
        major: '历史学',
        applyDate: '2024-05-07 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '困难',
        difficultType: '困难',
        gender: '女',
        birthday: '1997-07-01',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 4,
        studentNo: '01120180',
        name: '吴睿泊',
        college: '法学院',
        major: '法学',
        applyDate: '2024-02-04 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '女',
        birthday: '2000-11-25',
        bloodType: 'A型',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 5,
        studentNo: '01120068',
        name: '范宸坤',
        college: '法学院',
        major: '法学',
        applyDate: '2024-01-25 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '男',
        birthday: '1997-07-01',
        bloodType: 'A型',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 6,
        studentNo: '201001',
        name: '贾舞恒',
        college: '现代工程与应用科学学院',
        major: '能源科学与工程',
        applyDate: '2024-01-25 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '男',
        birthday: '',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 7,
        studentNo: '201002',
        name: '贾瑞茹',
        college: '现代工程与应用科学学院',
        major: '能源科学与工程',
        applyDate: '2024-01-25 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '男',
        birthday: '',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 8,
        studentNo: '201003',
        name: '李相兵',
        college: '现代工程与应用科学学院',
        major: '能源科学与工程',
        applyDate: '2024-01-25 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '男',
        birthday: '',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 9,
        studentNo: '201004',
        name: '王伟',
        college: '现代工程与应用科学学院',
        major: '能源科学与工程',
        applyDate: '2024-01-25 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '男',
        birthday: '',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
      {
        id: 10,
        studentNo: '201005',
        name: '赵敏',
        college: '现代工程与应用科学学院',
        major: '能源科学与工程',
        applyDate: '2024-01-25 00:00:00',
        applyReason: '',
        year: '2023-2024学年',
        term: '不分学期',
        applyType: '一般困难',
        difficultType: '一般困难',
        gender: '女',
        birthday: '',
        bloodType: '',
        nation: '汉族',
        idType: '居民身份证',
        idNo: '320112...',
      },
    ],
  columns,
  useSearchForm: true,
  formConfig,
  showIndexColumn: true,
  rowKey: 'id',
});

const [registerExportModal, { openModal: openExportModal }] = useModal();

function handleExport() {
  openExportModal(true, { columnList: columns });
}
function handleDownload(data) {
  createMessage.success('导出成功');
}
</script>