<template>
    <BasicModal
      v-bind="$attrs"
      @register="registerModal"
      :title="getTitle"
      showOkBtn
      okText="保存"
      cancelText="关闭"
      @ok="handleSubmit"
    >
    <BasicForm @register="registerForm" />

    </BasicModal>
  </template>
  <script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  
  const emit = defineEmits(['register', 'reload']);
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      { field: 'department', label: '院系', component: 'Select', componentProps: { placeholder: '请选择' }, rules: [{ required: true, trigger: 'blur', message: '请输入院系' }] },
      { field: 'class', label: '班级', component: 'Select', componentProps: { placeholder: '请选择' }, rules: [{ required: true, trigger: 'blur', message: '请输入班级' }] },
      { field: 'schoolYear', label: '学年', component: 'Select', componentProps: {placeholder: '请选择' }, rules: [{ required: true, trigger: 'blur', message: '请输入学年' }] },
      { field: 'evaluationTime', label: '民主评议时间', component: 'DatePicker', componentProps: { placeholder: '请选择' }, rules: [{ required: true, trigger: 'blur', message: '请输入民主评议时间' }] },
      { field: 'participants', label: '参与人数', component: 'InputNumber', componentProps: { placeholder: '请输入' }, rules: [{ required: true, trigger: 'blur', message: '请输入参与人数' }] },
      {
      field: 'bodyText',
      label: '评议总结',
      component: 'Textarea',
      componentProps: { placeholder: '请输入' },
    },
      { field: 'attachment', label: '附件', component: 'UploadFile', componentProps: { maxSize: 10 },helpMessage:'仅支持doc，jpg，png，jpeg，bmp，dox，zip，rar，pdf，xs，xsx，bt类型文件;文件大小10.0MB以内' },
    ],
    labelWidth:120
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');
  const getTitle = computed(() => (id.value ? '编辑' : '新建'));
  
  async function init(data) {
    changeLoading(true);
    resetFields();
    id.value = data?.id || '';
    if (id.value) {
      // 获取详情并赋值
      // setFieldsValue(res.data)
    }
    changeLoading(false);
  }
  async function handleSubmit() {
    const values = await validate();
    if (!values) return;
    changeOkLoading(true);
    // 提交逻辑
    changeOkLoading(false);
    closeModal();
    emit('reload');
  }
  </script>
  