<template>
  <Drawer v-bind="$attrs" title="填写统计" width="600"  @close="handleOk">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'count'">
              <span @click="handleOk" class="count-color">{{ record.count }}</span>
            </template>
          </template>
    </BasicTable>
    <template #footer>
    <a-button type="primary" @click="handleOk">关闭</a-button>
    </template>
  </Drawer>
</template>
<script lang="ts" setup>
import { Drawer } from 'ant-design-vue';
import { BasicTable, useTable, BasicColumn } from '@/components/Table';

const statColumns: BasicColumn[] = [
  { title: '民主评议单位', dataIndex: 'unit', resizable: true, ellipsis: true, width: 200 },
  { title: '填写数量', dataIndex: 'count', resizable: true, ellipsis: true, width: 100, align: 'right' },
];

const statData = [
  { unit: '能源与矿业工程学院', count: 1 },
];

const [registerTable] = useTable({
  columns: statColumns,
  dataSource: statData,
  showIndexColumn: true,
  showTableSetting:false,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'xn',
        label: '',
        component: 'Select',
        componentProps: { placeholder: '请选择学年' },
        colProps: { span: 8 },
      },{
        field: 'xy',
        label: '',
        component: 'Select',
        componentProps: { placeholder: '请选择学院' },
        colProps: { span: 8 },
      },
    ],
  },
});
const emits = defineEmits(['handleOk']);
const handleOk = (record) => {
    emits('handleOk',record);
  };
</script> 
<style scoped lang="less">  
.count-color{
  color: @primary-color;
  cursor: pointer;
}
</style>