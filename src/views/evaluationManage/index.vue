<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button preIcon="icon-ym icon-ym-btn-add" type="primary"
              @click="openDemocraticEvaluationFormModal()">新建</a-button>
            <a-button preIcon="icon-ym icon-ym-delete" type="error">删除</a-button>
            <a-button @click="handleImport" preIcon="icon-ym icon-ym-btn-upload" type="link">导入</a-button>
            <a-button @click="handleStatistic" preIcon="icon-ym icon-ym-extend-bar-chart" type="link">填写统计</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <DemocraticEvaluationForm @register="registerDemocraticEvaluationFormPopup" @reload="reload" />
    <ImportModal @register="registerImportModal" />
    <StatisticsDrawer :visible="visible" @handleOk="handleOk"/>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
import { usePopup } from '@/components/Popup';
import { useModal } from '@/components/Modal';
import DemocraticEvaluationForm from './DemocraticEvaluationForm.vue';
import StatisticsDrawer from './StatisticsDrawer.vue';
import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
const [registerTable,{reload}] = useTable({
  useSearchForm: true,
  rowSelection:{
    type: 'checkbox',
  },
  columns: [
    { title: '院系', dataIndex: 'department' },
    { title: '班级', dataIndex: 'class' },
    { title: '学年', dataIndex: 'schoolYear' },
    { title: '上报人姓名', dataIndex: 'reporterName' },
    { title: '上报人工号', dataIndex: 'reporterId' },
    { title: '民主评议时间', dataIndex: 'evaluationTime' },
    { title: '参与人数', dataIndex: 'participants' },
  ],
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
  },
  dataSource: [
    {
      department: '计算机学院',
      class: '22生物医学工程',
      schoolYear: '2024-2025学年',
      reporterName: '丁丹',
      reporterId: '01119197',
      evaluationTime: '2024-06-17',
      participants: 10,
      id:11
    },
    {
      department: '机械工程学院',
      class: '22机械1班',
      schoolYear: '2024-2025学年',
      reporterName: '李雷',
      reporterId: '01119198',
      evaluationTime: '2024-06-18',
      participants: 12,
    },
    {
      department: '电气工程学院',
      class: '22电气2班',
      schoolYear: '2024-2025学年',
      reporterName: '韩梅梅',
      reporterId: '01119199',
      evaluationTime: '2024-06-19',
      participants: 11,
    },
    {
      department: '管理学院',
      class: '22工商管理',
      schoolYear: '2024-2025学年',
      reporterName: '王五',
      reporterId: '01119200',
      evaluationTime: '2024-06-20',
      participants: 13,
    },
    {
      department: '外国语学院',
      class: '22英语1班',
      schoolYear: '2024-2025学年',
      reporterName: '赵六',
      reporterId: '01119201',
      evaluationTime: '2024-06-21',
      participants: 9,
    },
    {
      department: '材料学院',
      class: '22材料2班',
      schoolYear: '2024-2025学年',
      reporterName: '孙七',
      reporterId: '01119202',
      evaluationTime: '2024-06-22',
      participants: 14,
    },
    {
      department: '土木工程学院',
      class: '22土木1班',
      schoolYear: '2024-2025学年',
      reporterName: '周八',
      reporterId: '01119203',
      evaluationTime: '2024-06-23',
      participants: 8,
    },
    {
      department: '化学学院',
      class: '22化学1班',
      schoolYear: '2024-2025学年',
      reporterName: '吴九',
      reporterId: '01119204',
      evaluationTime: '2024-06-24',
      participants: 10,
    },
    {
      department: '数学学院',
      class: '22数学2班',
      schoolYear: '2024-2025学年',
      reporterName: '郑十',
      reporterId: '01119205',
      evaluationTime: '2024-06-25',
      participants: 15,
    },
    {
      department: '物理学院',
      class: '22物理1班',
      schoolYear: '2024-2025学年',
      reporterName: '钱十一',
      reporterId: '01119206',
      evaluationTime: '2024-06-26',
      participants: 7,
    },
  ],
  formConfig: {
    schemas: [
      { field: 'schoolYear', label: '关键字', component: 'Input', componentProps: { placeholder: '请输入上报人姓名/上报人工号' } },
      { field: 'department', label: '学年', component: 'Select', componentProps: { placeholder: '全部' } },
    ],
  },
});
const [registerDemocraticEvaluationFormPopup, { openModal: openDemocraticEvaluationFormModal }] = useModal();
const [registerImportModal, { openModal: openImportModal }] = useModal();
function getTableActions(record): any[] {
  return [
    {
      label: '编辑',
      onClick: () => {
        openDemocraticEvaluationFormModal(true, {
          id:record.id,
        });
      },
    },
  ];
}
function handleImport() {
  openImportModal(true,{});
}
const visible = ref(false);
function handleStatistic() {
  visible.value = true;
}

function handleOk() {
  visible.value = false;
  reload();
}
</script>
