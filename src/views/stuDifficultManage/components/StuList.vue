<template>
  <BasicModal v-bind="$attrs" class="transfer-modal member-modal" @register="registerModal" title="学生列表" :width="1000">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
    <template #footer>
      <a-button @click="handleSubmit" type="primary">确定</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicTable, TableAction, useTable } from '@/components/Table';
  import { studentAllList } from '@/api/school/jbxx';
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(() => {
    reload();
  });

  const columns: BasicColumn[] = [
    { title: '学号', fixed: 'left', dataIndex: 'xh', width: 140 },
    { title: '姓名', fixed: 'left', dataIndex: 'xm', width: 80 },
    { title: '性别', dataIndex: 'xbmc', width: 60 },
    { title: '出生日期', dataIndex: 'csrq', width: 130 },
    { title: '院系', dataIndex: 'dwmc', width: 150 },
    { title: '专业', dataIndex: 'zymc', width: 150 },
    { title: '现在年级', dataIndex: 'xznj', width: 80, fixed: 'right' },
    { title: '预计毕业日期', dataIndex: 'yjbyrq', width: 130 },
    { title: '学制', dataIndex: 'xz', width: 60 },
  ];

  const [registerTable, { reload, getForm, getSelectRows }] = useTable({
    api: studentAllList,
    columns,
    immediate: false,
    useSearchForm: true,
    formConfig: getFormConfig(),
    showTableSetting: false,
    canResize: true,
    rowSelection: { type: 'radio' },
    maxHeight: 400,
  });

  function getFormConfig(): Partial<FormProps> {
    return {
      schemas: [
        {
          field: 'keyword',
          label: '关键词',
          component: 'Input',
          componentProps: {
            placeholder: '请输入关键词',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            clearImmediate: false,
          },
        },
      ],
    };
  }
  const emit = defineEmits(['register','select']);
  function handleSubmit() {
    const data = getSelectRows()[0];
    emit('select', data);
    closeModal();
  }
</script>
