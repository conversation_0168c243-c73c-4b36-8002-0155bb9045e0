<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button type="primary" @click="handleExport">导出当前表格</a-button>
            <a-button @click="handleExport">导出答题明细</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 表格列插槽区 -->
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === '正常' ? 'success' : 'error'">
                {{ record.status }}
              </a-tag>
            </template>
            <template v-if="column.key === 'enrollmentStatus'">
              {{ record.enrollmentStatus === '未参加' ? '未参加' : '已参加' }}
            </template>
            <template v-if="column.key === 'dateOfBirth'">
              {{ record.dateOfBirth | formatDate }}
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 导出 -->
    <ExportModal @register="registerExportModal" />
  </div>
</template>

<script lang="ts" setup>
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import { onMounted, ref } from 'vue';

  // #region table
  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '学号',
      dataIndex: 'studentId',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: 90,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '出生日期',
      dataIndex: 'dateOfBirth',
      width: 150,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '院系',
      dataIndex: 'department',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'major',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'class',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'currentGrade',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学籍状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '参加情况',
      dataIndex: 'enrollmentStatus',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写学年',
      dataIndex: 'txxn',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写学期',
      dataIndex: 'txxq',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
  ];

  // 注册表格
  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    // 表格数据接口
    dataSource: [
      {
        studentId: '0111924',
        name: '张三',
        gender: '男',
        dateOfBirth: '2000-01-01',
        department: '经济管理学院',
        major: '经济学',
        class: '经济学15',
        currentGrade: 2020,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '01119432',
        name: '李四',
        gender: '男',
        dateOfBirth: '1999-01-01',
        department: '软件学院',
        major: '应用软件工程',
        class: '22JAVA开发1',
        currentGrade: 2022,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '01120014',
        name: '王五',
        gender: '女',
        dateOfBirth: '1998-09-08',
        department: '现代工程与应用科学学院',
        major: '能源科学与工程',
        class: '能源科学与工程',
        currentGrade: 2018,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '01120068',
        name: '赵六',
        gender: '男',
        dateOfBirth: '1997-07-01',
        department: '法学院',
        major: '法学',
        class: '法学理论18',
        currentGrade: 2020,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '01120180',
        name: '钱七',
        gender: '女',
        dateOfBirth: '2000-11-25',
        department: '法学院',
        major: '法学',
        class: '法学理论16',
        currentGrade: 2024,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '01120165',
        name: '孙八',
        gender: '女',
        dateOfBirth: '1997-07-01',
        department: '现代工程与应用科学学院',
        major: '能源科学与工程',
        class: '材料科学与工程',
        currentGrade: 2021,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '01121166',
        name: '周九',
        gender: '男',
        dateOfBirth: '1991-03-06',
        department: '经济管理学院',
        major: '国际经济与贸易',
        class: '国际经济与贸易',
        currentGrade: 2021,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '15030057',
        name: '吴十',
        gender: '女',
        dateOfBirth: '1997-04-10',
        department: '现代工程与应用科学学院',
        major: '材料科学与工程',
        class: '材料科学与工程',
        currentGrade: 2018,
        status: '正常',
        enrollmentStatus: '未参加',
      },
      {
        studentId: '18010101',
        name: '郑十一',
        gender: '男',
        dateOfBirth: '1999-10-01',
        department: '经济管理学院',
        major: '国际经济与贸易',
        class: '国际经济与贸易',
        currentGrade: 2018,
        status: '正常',
        enrollmentStatus: '未参加',
      },
    ],
    columns,
  });

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: () => {},
      },
    ];
  }

  // #endregion

  // #region 导出
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  function handleExport() {
    openExportModal(true, { columnList: columns });
  }
  // #endregion
</script>
