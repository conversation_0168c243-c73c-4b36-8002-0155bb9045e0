<template>
  <div class="ml-20px mr-20px">
    <switchTab lbl="学生类别" :tabs="xslbArr" onlyName="fullName" onlyKey="enCode" @changeTabItem="item => changeTabItem(item, 'xslb')"></switchTab>
    <switchTab lbl="现在年级" :tabs="njArr" onlyName="njmc" onlyKey="nj" @changeTabItem="item => changeTabItem(item, 'xznj')"></switchTab>
    <div class="con-box mt-10px">
      <BasicTable @register="registerTable"> </BasicTable>
      <eChart ref="eChartRef" :options="eChartObj.options" :height="450" @reload=""></eChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useBaseStore } from '@/store/modules/base';
  import { BasicTable, useTable } from '@/components/Table';
  import * as schoolApi from '@/api/school';
  import switchTab from './components/switchTab.vue';
  import eChart from '@/views/questionManage/statistic/components/chart.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const props = defineProps(['wjdm']);
  const api = useBaseApi('/api/knsDcwj');

  const baseStore = useBaseStore();
  const njArr = ref([]);
  const xslbArr = ref([]);
  const columns = [
    { title: '院系名称', dataIndex: 'dwmc' },
    { title: '已参加人数', dataIndex: 'examineCount', width: 130, align: 'right' },
    { title: '未参加人数', dataIndex: 'notExamineCount', width: 130, align: 'right' },
    { title: '总人数', dataIndex: 'allCount', width: 80, align: 'right', fixed: 'right' },
  ];
  const [registerTable, { reload, setTableData }] = useTable({
    api: params => api.request('get', '/listDwStatistics', { params }),
    beforeFetch: params => {
      const data = {
        ...params,
        ...query,
        wjdm: props.wjdm,
      };
      return data;
    },
    afterFetch: data => {
      // 更新图表数据
      if (data && data.length > 0) {
        const dwmcList = data.map(item => item.dwmc);
        const notExamineCountList = data.map(item => item.notExamineCount || 0);
        const examineCountList = data.map(item => item.examineCount || 0);

        // 更新 eChart 配置
        eChartObj.options.yAxis.data = dwmcList;
        eChartObj.options.series[0].data = notExamineCountList;
        eChartObj.options.series[1].data = examineCountList;
      }
      return data;
    },
    columns,
    immediate: false,
    useSearchForm: false,
    clickToRowSelect: false,
    pagination: false,
    showTableSetting: false,
  });

  const ALL_OPTION = {
    nj: '',
    njmc: '全部',
    enCode: '',
    fullName: '全部',
  };

  async function getOptions() {
    const xslbList = await baseStore.getDictionaryData('xslb');
    xslbArr.value = [
      {
        enCode: '',
        fullName: '全部',
      },
      ...(xslbList as any[]),
    ];

    // const { data: xyList } = await schoolApi.getXY({ pageSize: 99999 });
    const res = await schoolApi.getCollegeGradeInfo({ pageSize: 99999 });
    debugger;
    njArr.value = [
      ALL_OPTION,
      ...njList.map(item => ({
        nj: item,
        njmc: item,
      })),
    ];
  }
  const query = reactive({
    nj: '',
  });
  async function changeTabItem(item, key) {
    query[key] = item;
    reload();
  }

  const eChartObj = reactive({
    options: {
      legend: {
        data: ['未参加', '已参加'],
        orient: 'horizontal',
        top: 'top',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '5%',
        right: '20%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        data: [],
      },
      series: [
        {
          name: '未参加',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'insideRight',
          },
          data: [],
          itemStyle: {
            color: '#F5A12F',
          },
        },
        {
          name: '已参加',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'insideRight',
          },
          data: [],
          itemStyle: {
            color: '#28D988',
          },
        },
      ],
    },
  });
  onMounted(() => {
    getOptions();
    reload();
  });
</script>
<style scoped lang="less">
  .con-box {
    display: grid;
    grid-template-columns: 40% 60%;
    // padding: 15px 10px 0px 10px;
    .active {
      color: @primary-color;
      cursor: pointer;
    }
    .chart-container {
      width: 100%;
      height: 100%;
      padding: 10px 0;
      background-color: white;
    }
  }
</style>
