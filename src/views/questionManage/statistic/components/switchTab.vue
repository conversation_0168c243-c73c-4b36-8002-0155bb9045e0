<!--
 * @Description: tab条件组件
 * @Autor: panmy
 * @Date: 2024-08-28 15:37:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 16:08:46
-->
<template>
  <div class="query-item mr-50px">
    <p>{{ props.lbl }}：</p>
    <div class="query-item-select">
      <span
        :class="{ active: itemValue.includes(item[props.onlyKey]) }"
        v-for="item in tabs"
        :key="item[props.onlyKey]"
        :value="item[props.onlyKey]"
        @click.stop="handleChangeTabItem(item)">
        <template v-if="props.onlyName != ''">{{ item[props.onlyName] }}</template>
        <template v-else>{{ item.fullName || item.pcmc || item.zdmc }}</template>
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { defineProps, defineEmits, watch, ref, toRaw } from 'vue';
  const props = defineProps({
    // 选项列表数据
    tabs: { type: Array, default: () => [] },
    // 唯一标识字段名
    onlyKey: { type: String, default: '' },
    // 显示字段名
    onlyName: { type: String, default: '' },
    // 标签名
    lbl: { type: String, default: '' },
    // 当前选中值
    value: { type: [String, Number, Array], default: '' },
    /// 是否多选
    isMultiple: { type: Boolean, default: false },
  });

  const emit = defineEmits(['changeTabItem']);
  const itemValue = ref<Array | string>(props.isMultiple ? [props.value] : props.value);

  function handleChangeTabItem(item) {
    if (props.isMultiple) {
      const index = itemValue.value.indexOf(item[props.onlyKey]);
      if (index > -1) {
        itemValue.value.splice(index, 1);
      } else {
        itemValue.value.push(item[props.onlyKey]);
      }
      emit('changeTabItem', itemValue.value);
    } else {
      itemValue.value = [item[props.onlyKey]];
      emit('changeTabItem', item[props.onlyKey]);
    }
  }

  watch(
    () => props.value,
    val => {
      itemValue.value = Array.isArray(val) ? val : [val];
    },
    { immediate: true },
  );
</script>
<style lang="less" scoped>
  .query-item {
    display: flex;
    align-items: center;
    background-color: #fff;

    p {
      color: #bbb;
      flex-shrink: 0;
      width: 70px;
      align-self: flex-start;
      margin-top: 10px;
    }

    .query-item-select {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      span {
        display: inline-block;
        cursor: pointer;
        padding: 3px 5px;
        margin: 5px 4px;
        box-sizing: border-box;
        border-radius: 3px;
        border: #e4e4e4 solid 1px;
        color: #333;
        cursor: pointer;
      }

      .active {
        color: #fff;
        background-color: @primary-color;
        border: 1px solid @primary-color;
      }
    }
  }
</style>
