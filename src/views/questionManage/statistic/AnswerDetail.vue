<template>
  <a-card v-for="item in answerList" :key="item.index" class="m-10px">
    <template #title>
      <div class="cardTitle">
        {{ `第${item.index}题` }}
        <a-tag color="processing">{{ item.type }}</a-tag>
      </div>
    </template>
    <div class="card-grid">
      <div class="card-left">
        <div class="card-name">{{ item.name }}</div>
        <div class="card-opt" v-if="item.options">
          <p v-for="child in item.options" :key="child.sub">{{ child.sub }} {{ child.label }}</p>
        </div>
      </div>
      <div class="card-right">
        <div class="card-name">
          共<span class="count" :class="{ 'scale-count': item.type === '简答题' }">{{ item.count }}</span
          >人作答
        </div>
        <div class="card-static" v-if="item.options">
          <p class="static-box" v-for="child in item.options" :key="child.sub">
            {{ child.sub }}
            <a-progress :percent="item.count ? (child.count / item.count) * 100 : 0" :format="percent => `${Math.round((percent / 100) * item.count)}人`" />
          </p>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { useBaseStore } from '@/store/modules/base';

  const props = defineProps(['wjdm']);
  const api = useBaseApi('/api/knsDcwj');
  const baseStore = useBaseStore();
  const answerList = ref([
    {
      index: 1,
      name: '家庭成员',
      type: '多选题',
      count: 10,
      options: [
        { label: '爸爸', value: 1, sub: '选项A', count: 4 },
        { label: '妈妈', value: 2, sub: '选项B', count: 3 },
        { label: '爷爷', value: 3, sub: '选项C', count: 3 },
        { label: '奶奶', value: 4, sub: '选项D', count: 0 },
      ],
    },
    {
      index: 2,
      name: '家庭年收入',
      type: '单选题',
      count: 100,
      options: [
        { label: '1~3万', value: 1, sub: '选项A', count: 80 },
        { label: '6万以上', value: 2, sub: '选项B', count: 20 },
      ],
    },
    {
      index: 3,
      name: '家庭情况',
      type: '简答题',
      count: 90,
    },
  ]);

  async function reload() {
    const res = await api.request('get', '/listDtStatistics', { params: { wjdm: props.wjdm } });
    if (res && res.data) {
      // 将接口返回的数据转换为模板所需的格式
      answerList.value = res.data.map((item, index) => {
        // 题目类型映射
        const typeMap = {
          '1': '单选题',
          '2': '多选题',
          '3': '简答题',
        };

        // 处理选项数据
        const options = item.xxList
          ? item.xxList.map((option, optIndex) => {
              // 计算该选项的作答人数
              const optionCount = option.dtmxList ? option.dtmxList.length : 0;

              return {
                label: option.xxmc || option.xxnr, // 选项名称或内容
                value: option.xxdm, // 选项代码
                sub: `选项${String.fromCharCode(65 + optIndex)}`, // A, B, C, D...
                count: optionCount,
              };
            })
          : null;

        return {
          index: parseInt(item.th) || index + 1, // 题号
          name: item.tmmc, // 题目名称
          type: typeMap[item.tmlx] || '未知题型', // 题目类型
          count: item.writeCount || 0, // 填写人数
          options: options,
        };
      });
    }
  }
  onMounted(() => {
    reload();
  });
</script>

<style lang="less" scoped>
  .cardTitle {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  .card-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    .card-name {
      color: #666666;
    }
    .count {
      color: @primary-color;
      margin: 0 4px;
    }
    .scale-count {
      font-size: 30px;
    }
    .card-left {
      border-right: solid #ccc 1px;
      .card-opt {
        font-size: 14px;
        margin-top: 10px;
        // font-weight: 500;
        p {
          margin: 8px 0;
        }
      }
    }
    .card-right {
      .card-static {
        color: #666666;
        margin-top: 10px;
        .static-box {
          display: grid;
          grid-template-columns: 50px 1fr;
          gap: 4px;
        }
      }
    }
  }
</style>
