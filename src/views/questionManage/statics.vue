<!--
 * @Description: 问卷统计
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 15:32:33
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <Spin :spinning="loading" size="large">
          <div class="fw-title">问卷统计</div>
          <div class="portalMain">
            <template v-for="(item, index) in batchList" :key="index">
              <div :class="['card', item.sfsy == 1 ? 'cardSel' : '']">
                <div class="cardTop">
                  <p class="cardTitle"> {{ item.txsm }}</p>
                  <p class="cardTemplate">
                    填写人数：<span>{{ item.txrs }}</span>
                  </p>
                </div>

                <div class="cardOpts">
                  <p class="link" @click="openPopup(true, item)">查看</p>
                </div>
              </div>
            </template>
          </div>
        </Spin>
        <Empty v-if="!batchList.length" />
      </div>
      <!--查看统计 -->
      <staticsPopup @register="registerStaticsPopup" @reload=""></staticsPopup>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { usePopup } from '@/components/Popup';
  import { useModal } from '@/components/Modal';
  import { Spin, Empty } from 'ant-design-vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import staticsPopup from './statistic/index.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/knsDcwj');
  const { createMessage } = useMessage();
  const [registerStaticsPopup, { openPopup }] = usePopup();
  const batchList = ref([
    {
      id: '1',
      wjmc: '家庭经济困难生调查表',
      sfqy: 0,
      count: 2,
    },
    {
      id: '2',
      wjmc: '困难生调查问卷',
      sfqy: 1,
      count: 30,
    },
  ]);
  const loading = ref(false);
  const reload = async () => {
    loading.value = true;
    const { data } = await api.request('get', '/listStatisticsAll', {});
    batchList.value = data;
    loading.value = false;
  };
  onMounted(() => {
    reload();
  });
</script>
<style lang="less" scoped>
  .mtcn-content-wrapper-content {
    background: #fff;
    position: relative;

    overflow: scroll !important;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .fw-tip {
    margin: 0 20px 20px 20px;
    color: #666666;
    font-size: 12px;
  }
  .fw-title {
    margin: 20px;
    padding-left: 16px;
    font-weight: bold;
    position: relative;
    font-size: 18px;
  }
  .fw-title::before {
    content: '';
    width: 4px;
    height: 15px;
    border-radius: 4px;
    background-color: @primary-color;
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 6px;
    transform: translate(-50%, -50%);
    margin-right: 8px;
  }
  .portalMain {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

    margin: 20px;
    grid-gap: 10px;
    .card {
      border: 1px solid #edeff2;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
      .cardTop {
        padding: 16px;
        .cardTitle {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          font-size: 16px;
          // font-weight: 700;
          margin-bottom: 16px;
        }
        .cardTemplate {
          margin-top: 4px;
          color: #86909c;
          font-size: 14px;
          line-height: 22px;
          span {
            color: #4e5969;
          }
        }
      }
      .cardOpts {
        padding: 11px 12px;
        border-top: 1px solid #edeff2;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .link {
          border-color: transparent;
          color: @primary-color;
          background: 0 0;
          display: inline-block;
          line-height: 1;
          white-space: nowrap;
          cursor: pointer;
        }
        .split {
          width: 1px;
          height: 12px;
          margin: 0 12px;
          background-color: #dfe2e8;
          flex-shrink: 0;
        }
      }
    }
    .cardSel::after {
      content: '已启用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #00b42a;
    }
  }
  .ml30 {
    margin-left: 30px;
  }
</style>
