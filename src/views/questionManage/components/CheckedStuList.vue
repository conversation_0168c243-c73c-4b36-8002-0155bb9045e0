<!--
 * @Description: 学生名单
 * @Autor: panmy
 * @Date: 2024-12-05 14:26:41
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 14:39:19
-->
<template>
  <BasicTable @register="registerSelectedTable">
    <template #tableTitle>
      <a-button type="primary" @click="handleAdd" preIcon="icon-ym icon-ym-btn-add">新增</a-button>
      <a-button type="error" color="error" @click="handleDelete" preIcon="icon-ym icon-ym-delete">批量删除</a-button>
      <a-button @click="handleExport" type="link" preIcon="icon-ym icon-ym-btn-download">导出</a-button>
      <a-button @click="handleImport" type="link" preIcon="icon-ym icon-ym-btn-upload">导入</a-button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <TableAction :actions="getTableActions(record)" :dropDownActions="[]" />
      </template>
    </template>
  </BasicTable>
  <!-- 导入 -->
  <ImportModalBrevity @register="registerImportModal" @reload="reload" />
  <!-- 导出 -->
  <ExportModal @register="registerExportModal" />
  <!-- 添加学生 -->
  <StuList @register="registerModal" @reload="reload"></StuList>
</template>
<script lang="ts" setup>
  import { reactive, watch, ref, toRefs, unref, computed, nextTick, onMounted } from 'vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ImportModalBrevity } from '@/components/CommonModal';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import StuList from './StuList.vue';
  import * as schoolApi from '@/api/school';
  import { stubFalse } from 'lodash-es';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const emit = defineEmits(['register', 'reload']);
  const props = defineProps(['stepIds', 'stepItems', 'shareInfo']);
  const api = useBaseApi('/api/knsDcwjPerson');
  const state = reactive({
    dwdm: '',
    zydm: '',
    nj: '',
    bjdm: '',
  });

  const { t } = useI18n();

  const { createMessage, createConfirm } = useMessage();

  // #region 已选学生数据表格
  const [registerSelectedTable, { reload, setTableData, getDataSource, getForm, getSelectRowKeys, getFetchParams }] = useTable({
    api: params => api.getList({ params }),
    columns: [
      { title: '学号', fixed: 'left', dataIndex: 'xsbh', width: 100 },
      { title: '姓名', fixed: 'left', dataIndex: 'xm', width: 80 },
      { title: '院系', dataIndex: 'dwmc', width: 150 },
      { title: '专业', dataIndex: 'zymc', width: 150 },
      { title: '年级', dataIndex: 'xznj', width: 80 },
      { title: '班级', dataIndex: 'bjmc', width: 100 },
      { title: '学生类别', dataIndex: 'xslbmc', width: 100 },
    ],
    beforeFetch: params => {
      params.wjdm = props.shareInfo.wjdm;
    },
    immediate: false,
    clickToRowSelect: true,
    rowSelection: {
      type: 'checkbox',
    },
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: {
            placeholder: '输入学号/姓名关键字搜索',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            clearImmediate: false,

            onChange: (val, obj) => {
              loadZY(val);
            },
          },
        },
        {
          field: 'zydm',
          label: '专业',
          component: 'Select',
          componentProps: {
            placeholder: '全部',

            onChange: (val, obj) => {
              loadNJ(val);
            },
          },
        },
        {
          field: 'nj',
          label: '年级',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            onChange: (val, obj) => {
              loadBJ(val);
            },
          },
        },
        {
          field: 'bjdm',
          label: '班级',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
          },
        },
      ],
    },
    useSearchForm: true,
    showTableSetting: false,
    // rowSelection: {
    //   type: 'checkbox',
    // },
  });
  // #region 院系 年级  专业 班级
  function loadXY() {
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      const form = getForm();
      form.updateSchema({ field: 'dwdm', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
      form.setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    });
  }
  async function loadZY(dwdm) {
    const form = getForm();
    form.updateSchema({ field: 'zydm', componentProps: { options: [] } });
    form.updateSchema({ field: 'nj', componentProps: { options: [] } });
    form.setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    schoolApi.getZY({ dwdm: dwdm, pageSize: 99999 }).then(res => {
      form.updateSchema({ field: 'zydm', componentProps: { options: res.data.list, fieldNames: { label: 'zyMc', value: 'zyDm' } } });
    });
  }
  function loadNJ(zydm) {
    const form = getForm();
    form.updateSchema({ field: 'nj', componentProps: { options: [] } });
    form.setFieldsValue({ nj: '', bjdm: '' });
    const state = form.getFieldsValue();

    schoolApi.getNJ({ dwdm: state.dwdm, zydm: zydm, pageSize: 99999 }).then(res => {
      const data = res.data.list.map(item => {
        return { label: item, value: item };
      });
      form.updateSchema({ field: 'nj', componentProps: { options: data, fieldNames: { label: 'label', value: 'value' } } });
    });
  }

  function loadBJ(nj) {
    const state = getForm().getFieldsValue();
    schoolApi.getBJ({ dwdm: state.dwdm, zydm: state.zydm, nj: nj }).then(res => {
      getForm().updateSchema({ field: 'bjdm', componentProps: { options: res.data.list, fieldNames: { label: 'bjMc', value: 'bjDm' } } });
    });
  }
  //#endregion

  function handleDelete() {
    const ids = getSelectRowKeys();
    if (!ids || ids.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确认要批量删除吗？',
      onOk: () => {
        api.batchRemove({ params: { ids: ids.join(',') } }).then(() => {
          createMessage.success('删除成功');
          reload();
        });
      },
    });
  }

  const [registerModal, { openModal }] = useModal();
  function handleAdd() {
    openModal(true, { wjdm: props.shareInfo.wjdm });
  }
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  function handleImport(queryObj = {}) {
    openImportModal(true, {
      type: 'position',
      isUploadDirectly: true,
      actionUrl: '/api/knsDcwjPerson/importPerson',
      queryObj: { wjdm: props.shareInfo.wjdm },
      downloadTemplateUrl: '/api/knsDcwjPerson/downloadTemplate',
    });
  }
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  async function handleExport() {
    const listQuery = getFetchParams();
    openExportModal(true, { listQuery, exportType: 'KnsDcwjPersonImport', apiUrl: '/api/knsDcwjPerson/export' });
  }

  function save() {
    return {
      success: true,
      id: '',
      errorMessage: '',
    };
  }
  const checkValidate = async () => {
    const {
      data: { list = [] },
    } = await api.getList({ params: { pageSize: 9999, currentPage: 1, wjdm: props.shareInfo.wjdm } });
    if (list.length === 0) throw new Error('请至少选择学生名单!');
    return {
      success: true,
    };
  };
  function getDetail() {
    reload();
  }
  defineExpose({ save, getDetail, checkValidate });
  onMounted(() => {
    loadXY();
  });
</script>
