<!--
 * @Description: 资助明细
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 11:59:07
-->
<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    cancel-text="关闭"
    title="资助项目列表"
  >
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <!-- 按钮组区 -->
        <a-button
          type="primary"
          preIcon="icon-ym icon-ym-btn-add"
          @click="addOrUpdateHandle({})"
          >新增</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
              <TableAction
                :actions="getTableActions(record)"
              />
            </template>
      </template>
    </BasicTable>
    <AddFundingDetailForm @register="registerAddFundingDetailForm" />
  </BasicPopup>
</template>

<script lang="ts" setup>
import { BasicPopup, usePopupInner } from '@/components/Popup';
import {useModal} from '@/components/Modal';
import { BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
import AddFundingDetailForm from './components/AddFundingDetailForm.vue';
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] =
  usePopupInner(() => {
    reload();
  });

const columns: BasicColumn[] = [
  {
    title: '资助项目名称',
    dataIndex: 'sponsorProjectName',
    align: 'left',
    width: 200,
    resizable: true,
    ellipsis: true,
    
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    align: 'right',
    width: 100,
    resizable: true,
    ellipsis: true,
    
  },
  {
    title: '是否启用',
    dataIndex: 'enabledMark',
    align: 'center',
    width: 120,
    resizable: true,
    ellipsis: true,
    
  },
  {
    title: '关联业务',
    dataIndex: 'relatedBusiness',
    align: 'left',
    width: 150,
    resizable: true,
    ellipsis: true,
    
  },
];

const [registerTable, { reload, getForm }] = useTable({
  columns,
  immediate: false,
  useSearchForm: true,
  formConfig: getFormConfig(),
  showTableSetting: false,
  actionColumn: {
    width: 80,
    title: '操作',
    align: 'center',
    dataIndex: 'action',
  },
  dataSource:[
  {
    index: 1,
    sponsorProjectName: '勤工助学',
    sortOrder: 1,
    enabledMark: '是',
    relatedBusiness: '勤工助学',
  },
  {
    index: 2,
    sponsorProjectName: '助学金',
    sortOrder: 2,
    enabledMark: '是',
    relatedBusiness: '助学金',
  },
  {
    index: 3,
    sponsorProjectName: '困遗补助',
    sortOrder: 3,
    enabledMark: '是',
    relatedBusiness: '困遗补助',
  },
  {
    index: 4,
    sponsorProjectName: '奖学金',
    sortOrder: 4,
    enabledMark: '是',
    relatedBusiness: '奖学金',
  },
  {
    index: 5,
    sponsorProjectName: '生源地贷款',
    sortOrder: 5,
    enabledMark: '是',
    relatedBusiness: '困遗补助',
  },
  {
    index: 6,
    sponsorProjectName: '学费缓缴减免',
    sortOrder: 6,
    enabledMark: '否',
    relatedBusiness: '勤工助学',
  },
  {
    index: 7,
    sponsorProjectName: '服兵役',
    sortOrder: 7,
    enabledMark: '否',
    relatedBusiness: '助学金',
  },
  {
    index: 8,
    sponsorProjectName: '基层就业',
    sortOrder: 8,
    enabledMark: '否',
    relatedBusiness: '助学金',
  },
  {
    index: 9,
    sponsorProjectName: '专项奖学金',
    sortOrder: 9,
    enabledMark: '是',
    relatedBusiness: '奖学金',
  },
  {
    index: 10,
    sponsorProjectName: '临时困难补助',
    sortOrder: 10,
    enabledMark: '是',
    relatedBusiness: '困遗补助',
  },
]
});

const [registerAddFundingDetailForm, { openModal }] = useModal();
function getFormConfig(): Partial<FormProps> {
  return {
    schemas: [
      {
        field: 'keyword',
        label: '关键字',
        component: 'Input',
        componentProps: {
          placeholder: '请输入关键字',
          submitOnPressEnter: true,
        },
      },
    ],
  };
}

function getTableActions(record): any[] {
  return [
    {
      label: '编辑',
      onClick: () => addOrUpdateHandle(record),
    },
    {
      label: '删除',
      color: 'error',
      modelConfirm: {
        onOk: () => handleDelete(record),
      },
    },
  ];
}


function addOrUpdateHandle(record) {
  openModal(true,record);
}

function handleEdit(record) {
  // 编辑逻辑
  console.log('编辑', record);
}

function handleDelete(record) {
  // 删除逻辑
  console.log('删除', record);
}
</script>