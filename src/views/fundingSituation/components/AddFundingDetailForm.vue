<!--
 * @Description: 新增资助明细表单
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 11:59:07
-->
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
// import {
//   getStudentAidProjectsSelector,
//   getStudentAidProjectInfo as getInfo,
//   createStudentAidProject as create,
//   updateStudentAidProject as update,
// } from "@/api/studentAidProject";
import { ref, unref, computed } from "vue";
import { BasicModal, useModalInner } from "@/components/Modal";
import { BasicForm, useForm } from "@/components/Form";
import { useMessage } from "@/hooks/web/useMessage";

const emit = defineEmits(["register", "reload"]);
const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] =
  useForm({
    schemas: [
      {
        field: "projectName",
        label: "资助项目名称",
        component: "Input",
        componentProps: { placeholder: "请输入", maxlength: 50 },
        rules: [
          { required: true, trigger: "blur", message: "请输入资助项目名称" }
        ],
      },
      {
        field: "sort",
        label: "排序",
        defaultValue: 1,
        component: "InputNumber",
        componentProps: { min: 0, max: 999999 },
      },
      {
        field: "isEnabled",
        label: "是否启用",
        defaultValue: 1,
        component: "Radio",
        componentProps: {
          options: [
            { fullName: "是", id: 1 },
            { fullName: "否", id: 0 }
          ]
        },
      },{
        field: "isEnabled",
        label: "可选择的业务",
        defaultValue: 1,
        component: "Select",
        componentProps: {
          options: [
            { fullName: "是", id: 1 },
            { fullName: "否", id: 0 }
          ],
          multiple:true
        },
      },
    ],
    labelWidth: 100,
  });
const [registerModal, { closeModal, changeLoading, changeOkLoading }] =
  useModalInner(init);
const id = ref("");
const { createMessage } = useMessage();

const getTitle = computed(() => (unref(id) ? "编辑" : "新建"));

async function init(data) {
  changeLoading(true);
  resetFields();
  id.value = data.id;
  if (id.value) {
    // getInfo(id.value).then((res) => {
    //   // 处理布尔类型转换为数字
    //   res.data.isEnabled = Number(res.data.isEnabled);
    //   setFieldsValue(res.data);
    //   changeLoading(false);
    // });
  }
  changeLoading(false);
}

async function handleSubmit() {
  try {
    const values = await validate();
    changeOkLoading(true);
    const query = {
      ...values,
      id: id.value,
    };
    // const formMethod = id.value ? update : create;
    // const res = await formMethod(query);
    // createMessage.success(res.msg);
    // changeOkLoading(false);
    // closeModal();
    // emit("reload");
  } catch (error) {
    changeOkLoading(false);
  }
}
</script>