<!--
 * @Description: 受资助情况
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 11:59:07
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <!-- 左侧tableTitle按钮组区 -->
          <template #tableTitle>
            <a-button
              type="primary"
              preIcon="icon-ym icon-ym-btn-add"
              @click="hadleFundingDetails()"
              >资助明细项目管理</a-button
            >
           
            <a-button
              type="link"
              @click="handleExport"
              preIcon="icon-ym icon-ym-btn-download button-preIcon"
              >导出</a-button
            >
          
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 表格列插槽区 -->
            <template v-if="column.key === 'fundStatus'">
              <a-tag
                :color="
                  record.fundStatus == 0
                    ? 'success'
                    : record.fundStatus == 1
                    ? 'warning'
                    : 'error'
                "
              >
                {{ record.fundStatus === 0 ? '正常' : record.fundStatus === 1 ? '部分资助' : '未资助' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="getTableActions(record)"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 资助明细项目管理 -->
    <FundingDetails @register="registerFundingDetails" @reload="reload" />
    <!-- 导出 -->
    <ExportModal @register="registerExportModal" @download="handleDownload" />
    <!-- 资助详情 -->
     <FundingStatic @register="registerFundingStatic" />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from "@/components/Modal";
import { usePopup } from "@/components/Popup";
import {
  ActionItem,
  BasicColumn,
  BasicTable,
  FormProps,
  TableAction,
  useTable,
} from "@/components/Table";
// import { useMessage } from "@/hooks/web/useMessage";
import ExportModal from "@/components/CommonModal/src/ExportModal.vue";
import FundingDetails from "./FundingDetails.vue";
import FundingStatic from "./FundingStatic.vue";
// const { createMessage,createConfirm } = useMessage();

// 表格列配置
const columns: BasicColumn[] = [
  {
    title: "姓名",
    dataIndex: "name",
    width: 100,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: "学号",
    dataIndex: "studentId",
    width: 120,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: "学院",
    dataIndex: "college",
    width: 150,
    resizable: true,
    ellipsis: true,
  },
  {
    title: "专业",
    dataIndex: "major",
    width: 150,
    resizable: true,
    ellipsis: true,
  },
  {
    title: "班级",
    dataIndex: "class",
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: "现在年级",
    dataIndex: "currentGrade",
    width: 100,
    resizable: true,
    ellipsis: true,
  },
  {
    title: "困难生等级",
    dataIndex: "difficultyLevel",
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: "学年",
    dataIndex: "academicYear",
    width: 120,
    resizable: true,
    ellipsis: true,
  },
  {
    title: "资助目标(元)",
    dataIndex: "assistanceTarget",
    width: 100,
    resizable: true,
    ellipsis: true,
    align: "right",
  },
  {
    title: "已获资助(元)",
    dataIndex: "receivedAssistance",
    width: 100,
    resizable: true,
    ellipsis: true,
    align: "right",
  },
  {
    title: "差额(元)",
    dataIndex: "deficit",
    width: 100,
    align: "right",
    resizable: true,
    ellipsis: true,
  },
  {
    title: "勤工助学(元)",
    dataIndex: "workStudy",
    width: 120,
    align: "right",
    resizable: true,
    ellipsis: true,
  },
  {
    title: "助学金(元)",
    dataIndex: "scholarship",
    width: 120,
    align: "right",
    resizable: true,
    ellipsis: true,
  },
];

// 注册表格
const [
  registerTable,
  {
    reload,
    getFetchParams,
    getSelectRowKeys,
    clearSelectedRowKeys,
  },
] = useTable({
  // 表格数据接口
  // api: getUserList,
  columns,
  useSearchForm: true,
  formConfig: getFormConfig(),
  actionColumn: {
    width: 60,
    title: "操作",
    align: "center",
    dataIndex: "action",
  },
  showIndexColumn: false,
  clickToRowSelect: false,
  rowSelection: { type: 'checkbox' },
  dataSource:[
  {
    name: "祝琼",
    studentId: "01120068",
    college: "法学院",
    major: "法学",
    class: "法学理论18",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "梦瑶",
    studentId: "01120180",
    college: "法学院",
    major: "法学",
    class: "法学理论16",
    currentGrade: 2024,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "莫振恒",
    studentId: "201001",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2024,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "莫振",
    studentId: "201001",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "寇雅",
    studentId: "201002",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "寇茹",
    studentId: "201002",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "李兵",
    studentId: "201003",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "李柏",
    studentId: "201003",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "刘清",
    studentId: "201004",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
  {
    name: "刘水",
    studentId: "201004",
    college: "现代工程与应用学院",
    major: "能源科学工程",
    class: "能源科学工程...",
    currentGrade: 2020,
    difficultyLevel: "一般困难",
    academicYear: "2024-2025学年",
    assistanceTarget: 7500,
    receivedAssistance: 0,
    deficit: 7500,
    workStudy: 0,
    scholarship: 0,
    fundStatus: 2,
  },
]
});

function getFormConfig(): Partial<FormProps> {
  return {
    // 查询条件配置区
    schemas: [
      {
        field: "keyword",
        label: "关键字",
        component: "Input",
        componentProps: {
          placeholder: "请输入姓名/学号",
          submitOnPressEnter: true,
        },
      },
      {
        field: "amountRange",
        label: "差额",
        component: "Select",
        componentProps: {
          placeholder: "请选择",
          options: [
            { fullName: "全部", value: "all" },
            { fullName: "超额", value: "excess" },
            { fullName: "1-1000", value: "1-1000" },
            { fullName: "1001-2000", value: "1001-2000" },
            { fullName: "2001-3000", value: "2001-3000" },
            { fullName: "3001-4000", value: "3001-4000" },
            { fullName: "4000以上", value: "4000-above" },
          ],
        },
      },
      {
        field: "academicYear",
        label: "学年",
        component: "Select",
        componentProps: {
          placeholder: "请选择",
          options: [
            { fullName: "2024-2025学年", value: "2024-2025" },
            { fullName: "2023-2024学年", value: "2023-2024" },
            { fullName: "2022-2023学年", value: "2022-2023" },
            { fullName: "2021-2022学年", value: "2021-2022" },
            { fullName: "2020-2021学年", value: "2020-2021" },
          ],
        },
      },
      {
        field: "difficultyLevel",
        label: "困难生等级",
        component: "Select",
        componentProps: {
          placeholder: "请选择",
          options: [
            { fullName: "非困难生", value: "non-difficult" },
            { fullName: "困难", value: "difficult" },
            { fullName: "特殊困难", value: "special-difficult" },
          ],
        },
      },
    ],
  };
}

function getTableActions(record): ActionItem[] {
  return [
    {
      label: "详情",
      onClick: () => {
        openStaticPopup(true, { id: record.id });
      },
    },
  ];
}


// 导出
const [registerExportModal, { openModal: openExportModal }] = useModal();
function handleExport() {
  const listQuery = getFetchParams();
  openExportModal(true, { listQuery, columnList: columns });
}
function handleDownload() {
  
}




const [registerFundingStatic, { openPopup: openStaticPopup }] = usePopup();

const [registerFundingDetails, { openPopup: openFundingDetailsPopup }] = usePopup();
function hadleFundingDetails() {
  openFundingDetailsPopup(true, {});
}

</script>
