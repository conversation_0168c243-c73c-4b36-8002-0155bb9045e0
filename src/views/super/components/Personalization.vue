<template>
  <div class="box-grid">
    <a-card>
      <template #title>
        <div class="btn">
          <span class="point">页面导出</span>
          <a-button type="primary" @click="handleExport">导出</a-button>
        </div>

      </template>
      <BasicForm @register="registerExportForm" />
    </a-card>
    <a-card>
      <template #title>
        <div class="btn">
          <span class="point">页面导入</span>
          <a-button type="primary" @click="handleSave">保存</a-button>
        </div>
      </template>
      <BasicForm @register="registerImportForm" />

    </a-card>
  </div>
  <a-card class="mt-30px">
    <template #title>
      <span class="point">日志信息</span>
    </template>
    <MtcnTextarea v-model:value="logContent" :rows="15" readonly />
  </a-card>

</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import * as applicationApi from '@/api/tuPass/application';
import { getList, create } from '@/api/tuPass/personal';
import { useMessage } from '@/hooks/web/useMessage';
import { BasicForm, useForm } from '@/components/Form';
import useClipboard from 'vue-clipboard3';
const { createMessage } = useMessage();
const lowCodeAppId = ref('')
const logContent = ref('') // 日志内容

// 日志记录器类
class RequestLogger {
  public totalCount = 0
  public successCount = 0
  public failureCount = 0
  private failedItems: Array<{ item: any, error: any, requestIndex: number }> = []
  private currentRequestIndex = 0

  /**
   * 开始记录批量请求
   */
  startBatch() {
    this.totalCount = 0
    this.successCount = 0
    this.failureCount = 0
    this.failedItems = []
    this.currentRequestIndex = 0
    this.log('开始批量导入操作...')
  }

  /**
   * 记录单个请求开始
   */
  logRequestStart(item: any) {
    this.currentRequestIndex++
    this.totalCount++
    this.log(`正在处理第 ${this.currentRequestIndex} 条数据: ${item.fullName || item.enCode || '未知'}`)
  }

  /**
   * 记录请求成功
   */
  logRequestSuccess(item: any, requestIndex: number) {
    this.successCount++
    this.log(`✅ 第 ${requestIndex} 条数据导入成功: ${item.fullName || item.enCode || '未知'}`)
  }

  /**
   * 记录请求失败
   */
  logRequestFailure(item: any, error: any, requestIndex: number) {
    this.failureCount++
    const errorMsg = error?.message || error?.toString() || '未知错误'
    this.failedItems.push({
      item,
      error,
      requestIndex
    })
    this.log(`❌ 第 ${requestIndex} 条数据导入失败: ${item.fullName || item.enCode || '未知'}`)
    this.log(`    错误信息: ${errorMsg}`)
  }

  /**
   * 完成批量请求并生成总结
   */
  finishBatch() {
    this.log('\n' + '='.repeat(60))
    this.log('📊 批量导入操作完成总结')
    this.log('='.repeat(60))
    this.log(`总请求数: ${this.totalCount}`)
    this.log(`成功数量: ${this.successCount} ✅`)
    this.log(`失败数量: ${this.failureCount} ❌`)
    this.log(`成功率: ${this.totalCount > 0 ? ((this.successCount / this.totalCount) * 100).toFixed(2) : 0}%`)

    if (this.failedItems.length > 0) {
      this.log('\n📋 失败详情:')
      this.log('-'.repeat(40))
      // 按照请求序号排序
      this.failedItems
        .sort((a, b) => a.requestIndex - b.requestIndex)
        .forEach((failed, index) => {
          this.log(`${index + 1}. 第 ${failed.requestIndex} 条数据: ${failed.item.fullName || failed.item.enCode || '未知'}`)
          this.log(`   错误: ${failed.error?.message || failed.error?.toString() || '未知错误'}`)
          if (failed.error?.response?.data) {
            this.log(`   响应: ${JSON.stringify(failed.error.response.data)}`)
          }
          this.log('')
        })
    }

    this.log('='.repeat(60))
  }

  /**
   * 添加日志内容
   */
  private log(message: string) {
    logContent.value += message + '\n'
    // 自动滚动到底部
    setTimeout(() => {
      const textarea = document.querySelector('.mtcn-textarea textarea') as HTMLTextAreaElement
      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight
      }
    }, 100)
  }

  /**
   * 清空日志
   */
  clear() {
    logContent.value = ''
  }
}

// 创建日志记录器实例
const logger = new RequestLogger()
const checkedPage = ref([])
//表单
const [registerExportForm, { updateSchema, setFieldsValue, validate }] = useForm({
  schemas: [
    {
      field: 'app', component: 'Select', label: '应用',
      rules: [{ required: true, trigger: 'change', message: '应用不能为空' }],
      componentProps: {
        onChange: async (e) => {
          lowCodeAppId.value = e
          checkedPage.value = []
          setFieldsValue({
            export: [],
            content: ''
          })
          const d = {
            currentPage: 1,
            pageSize: 9999,
            type: 1,
            lowCodeAppId: e
          }
          const { data } = await getList(d)
          updateSchema({
            field: 'export',
            componentProps: {
              options: data.list,
              fieldNames: {
                label: 'fullName',
                value: 'id'
              }
            }
          })
        }
      }
    },
    {
      field: 'export', component: 'Select', label: '页面',
      rules: [{ required: true, trigger: 'change', message: '页面不能为空', type: 'array' }],
      componentProps: {
        onChange: async (e, values) => {
          checkedPage.value = values.map(item => {
            return {
              fullName: item.fullName,
              enCode: item.enCode,
              type: item.type,
              image: item.image,
              sortCode: item.sortCode,
              enabledMark: item.enabledMark,
              description: item.description,
            }
          })
          setFieldsValue({
            content: JSON.stringify(checkedPage.value)
          })
        },
        multiple: true
      }
    },
    {
      field: 'content', component: 'Textarea', label: '内容',
      rules: [{ required: true, trigger: 'change', message: '内容不能为空' }],
      componentProps: {
        rows: 6
      }
    },
  ],
});
const [registerImportForm, { validate: importValidate, updateSchema: importUpdateSchema }] = useForm({
  schemas: [
    {
      field: 'app', component: 'Select', label: '应用',
      rules: [{ required: true, trigger: 'change', message: '应用不能为空' }],
      componentProps: {
        onChange: async (e) => {
          lowCodeAppId.value = e
        }
      }
    },
    {
      field: 'import', component: 'Textarea', label: '页面',
      rules: [{ required: true, trigger: 'change', message: '页面不能为空' }],
      componentProps: {
        rows: 8,
      }
    },
  ],
});
async function getSysConfigInfo() {
  const params = {
    pageSize: 9999,
    currentPage: 1
  }
  const { data } = await applicationApi.getSystemList(params)
  const fieldNames = {
    label: 'name',
    value: 'id'
  }
  updateSchema({
    field: 'app',
    componentProps: {
      options: data.list,
      fieldNames
    }
  })
  importUpdateSchema({
    field: 'app',
    componentProps: {
      options: data.list,
      fieldNames,
    }
  })
}
const { toClipboard } = useClipboard();
async function handleExport() {
  if (checkedPage.value.length === 0) {
    createMessage.warning('请先选择要导出的页面');
    return;
  }
  await toClipboard(JSON.stringify(checkedPage.value));
  createMessage.success('复制成功');
}
async function handleSave() {
  const values = await importValidate()
  if (!values) return;

  // 清空之前的日志
  logger.clear()

  const data = JSON.parse(values.import);

  // 开始记录批量操作
  logger.startBatch()

  // 使用 Promise.allSettled 并行处理所有请求，确保所有请求都能完成
  const promises = data.map((item, index) => sendRequest(item, index + 1))
  await Promise.allSettled(promises)

  // 完成批量操作
  logger.finishBatch()

  // 显示最终结果消息
  const successCount = logger.successCount
  const failureCount = logger.failureCount
  const totalCount = logger.totalCount

  if (failureCount === 0) {
    createMessage.success(`批量导入完成！共 ${totalCount} 条数据全部导入成功`)
  } else {
    createMessage.warning(`批量导入完成！成功 ${successCount} 条，失败 ${failureCount} 条，请查看日志详情`)
  }
}

/**
 * 发送单个请求并记录日志
 * @param query 请求参数
 * @param requestIndex 请求序号
 */
async function sendRequest(query, requestIndex) {
  try {
    // 记录请求开始
    logger.logRequestStart(query)

    // 发送请求
    await create({ ...query, lowCodeAppId: lowCodeAppId.value })

    // 记录请求成功
    logger.logRequestSuccess(query, requestIndex)
  } catch (error) {
    // 记录请求失败
    logger.logRequestFailure(query, error, requestIndex)
  }
}

onMounted(() => {
  getSysConfigInfo();
})
</script>
<style scoped lang="less">
.box-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.btn {
  display: flex;
  justify-content: space-between;
}
</style>