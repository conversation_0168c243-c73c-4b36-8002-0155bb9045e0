<template>
   <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center bg-white">
      <a-tabs class="mtcn-content-wrapper-tabs" v-model:activeKey="activeKey" type="card">
          <a-tab-pane :key="1" tab="个性化页面设置" class="p-30px">
            <Personalization/>
          </a-tab-pane>
        </a-tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStoreWithOut } from '@/store/modules/user';
import Personalization from './components/Personalization.vue';

const activeKey = ref(1);
const router = useRouter();
const userStore = useUserStoreWithOut();

/**
 * 权限检查：验证当前用户是否为超管账户
 * 如果不是超管，则重定向到404页面
 */
function checkAdminPermission() {
  const userInfo = userStore.getUserInfo;
  
  // 检查用户信息是否存在
  if (!userInfo) {
    redirectTo404();
    return false;
  }
  
  // 检查是否为超管账户
  if (!userInfo.isAdministrator) {
    redirectTo404();
    return false;
  }
  
  return true;
}

/**
 * 重定向到404页面
 */
function redirectTo404() {
  router.replace('/404');
}

onMounted(() => {
  checkAdminPermission();
});
</script>
<style scoped lang="less"> 
:deep(.ant-tabs-content-holder){
  overflow-y: scroll !important;
}
</style>