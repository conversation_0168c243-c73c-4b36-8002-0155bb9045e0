<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center bg-white">
      <div class="m-20px">
        <switchTab lbl="评定学年" :tabs="xnArr" onlyName="fullName" onlyKey="enCode" @changeTabItem="item => changeTabItem(item, 'nj')"></switchTab>
        <div class="con-box">
          <BasicTable @register="registerTable">
            <template #tableTitle>
              <a-button type="primary" preIcon="icon-ym icon-ym-btn-download" @click="handleExport"> 导出 </a-button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction :actions="getTableActions(record)" />
              </template>
            </template>
          </BasicTable>
          <eChart ref="eChartRef" :options="eChartObj.options" :height="550" @reload=""></eChart>
        </div>
      </div>
      <ExportModal @register="registerExportModal" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useBaseStore } from '@/store/modules/base';
  import { BasicTable, useTable, BasicColumn, TableAction } from '@/components/Table';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import * as schoolApi from '@/api/school';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import switchTab from '@/views/questionManage/statistic/components/switchTab.vue';
  import eChart from '@/views/questionManage/statistic/components/chart.vue';
  const baseStore = useBaseStore();
  const router = useRouter();
  const xnArr = ref([]);
  const columns = [
    { title: '院系名称', dataIndex: 'dwmc', width: 120 },
    { title: '已审人数', dataIndex: 'approvedCount', width: 100, align: 'right' },
    { title: '待审人数', dataIndex: 'unmodifiedCount', width: 100, align: 'right' },
    { title: '总人数', dataIndex: 'totalCount', width: 80, align: 'right', fixed: 'right' },
  ];

  const [registerTable, { reload, setTableData }] = useTable({
    columns,
    useSearchForm: false,
    clickToRowSelect: false,
    pagination: false,
    // showTableSetting: false,
    dataSource: [
      { dwmc: '金融学院', approvedCount: 180, unmodifiedCount: 1, totalCount: 181 },
      { dwmc: '计算机学院', approvedCount: 17, unmodifiedCount: 2, totalCount: 19 },
      { dwmc: '外语学院', approvedCount: 16, unmodifiedCount: 3, totalCount: 19 },
      { dwmc: '数学与统计学院', approvedCount: 15, unmodifiedCount: 4, totalCount: 19 },
      { dwmc: '物理学院', approvedCount: 14, unmodifiedCount: 5, totalCount: 19 },
      { dwmc: '化学学院', approvedCount: 13, unmodifiedCount: 6, totalCount: 19 },
      { dwmc: '生物学院', approvedCount: 12, unmodifiedCount: 7, totalCount: 19 },
      { dwmc: '材料学院', approvedCount: 11, unmodifiedCount: 8, totalCount: 19 },
      { dwmc: '机械学院', approvedCount: 10, unmodifiedCount: 9, totalCount: 19 },
      { dwmc: '电气学院', approvedCount: 9, unmodifiedCount: 10, totalCount: 19 },
      { dwmc: '土木学院', approvedCount: 8, unmodifiedCount: 11, totalCount: 19 },
      { dwmc: '水利学院', approvedCount: 8, unmodifiedCount: 11, totalCount: 19 },
    ],
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
  });
  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '明细',
        onClick: () => {
          router.push({
            path: '/stuDifficultManage/index',
            query: {
              dwdm: record.dwmc,
            },
          });
        },
      },
    ];
  }
  async function getOptions() {
    const xnRes = await baseStore.getDictionaryData('xn');
    xnArr.value = [{ enCode: '', fullName: '全部' }, ...xnRes];
  }
  const query = reactive({
    xn: '',
  });
  async function changeTabItem(item, key) {
    if (item != '') query[key] = item;
  }
  const eChartObj = reactive({
    options: {
      legend: {
        data: ['待审人数', '已审人数'],
        orient: 'horizontal',
        top: 'top',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '5%',
        right: '20%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        data: [
          '金融学院',
          '计算机学院',
          '外语学院',
          '数学与统计学院',
          '物理学院',
          '化学学院',
          '生物学院',
          '材料学院',
          '机械学院',
          '电气学院',
          '土木学院',
          '水利学院',
        ],
      },
      series: [
        {
          name: '待审人数',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'insideRight',
          },
          data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
          itemStyle: {
            color: '#F5A12F',
          },
        },
        {
          name: '已审人数',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'insideRight',
          },
          data: [180, 17, 106, 15, 0, 130, 12, 11, 10, 90, 8],
          itemStyle: {
            color: '#28D988',
          },
        },
      ],
    },
  });
  // 导出
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  function handleExport() {
    openExportModal(true, { columnList: columns });
  }
  onMounted(() => {
    getOptions();
  });
</script>
<style scoped lang="less">
  .con-box {
    display: grid;
    grid-template-columns: 40% 60%;
    padding: 15px 10px 0px 0px;
    .active {
      color: @primary-color;
      cursor: pointer;
    }
    .chart-container {
      width: 100%;
      height: 100%;
      padding: 10px 0;
      background-color: white;
    }
  }
</style>
