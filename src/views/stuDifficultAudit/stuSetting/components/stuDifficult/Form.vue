<!--
 * @Description: 
 * @Autor: Fhz
 * @Date: 2025-04-24 15:42:28
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 16:03:06
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  // import { createDifficultStudentType as create, updateDifficultStudentType as update, getDifficultStudentTypeInfo as getInfo } from '@/api/difficultStudent';

  const emit = defineEmits(['register', 'reload']);
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        field: 'typeName',
        label: '类型名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入类型名称' }],
        ifShow: () => type.value != 3,
      },
      {
        field: 'gradeName',
        label: '等级名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入等级名称' }],
        ifShow: () => type.value == 3,
      },
      {
        field: 'target',
        label: '资助目标',
        component: 'Input',
      },
      {
        field: 'quotaRatio',
        label: '资助名额比例系数',
        component: 'InputNumber',
      },
      {
        field: 'minAnnualIncome',
        label: '年人均收入最小',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入年人均收入最小值' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'maxAnnualIncome',
        label: '年人均收入最大',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入年人均收入最大值' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'minSurveyScore',
        label: '问卷得分范围下限',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入问卷得分范围下限' }],
        ifShow: () => type.value == 3,
      },
      {
        field: 'maxSurveyScore',
        label: '问卷得分范围上限',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入问卷得分范围上限' }],
        ifShow: () => type.value == 3,
      },
      {
        field: 'isActive',
        label: '是否使用',
        component: 'Switch',
        rules: [{ required: true, message: '请选择是否使用' }],
      },
    ],
    labelWidth: 140,
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');
  const { createMessage } = useMessage();

  const getTitle = computed(() => (type.value == 3 ? '困难生等级' : '困难生类型'));
  const type = ref('');
  async function init(data) {
    // changeLoading(true);
    resetFields();
    type.value = data.type;
    id.value = data.id;
    // await getInfo(id.value).then(res => {
    //   setFieldsValue(res.data);
    //   res.data.isActive = Number(res.data.isActive);
    //   changeLoading(false);
    // });
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;
    changeOkLoading(true);
    const query = {
      ...values,
      id: id.value,
    };
    // const formMethod = id.value ? update : create;
    // formMethod(query)
    //   .then(res => {
    //     createMessage.success(res.msg);
    //     changeOkLoading(false);
    //     closeModal();
    //     emit('reload');
    //   })
    //   .catch(() => {
    //     changeOkLoading(false);
    //   });
  }
</script>
