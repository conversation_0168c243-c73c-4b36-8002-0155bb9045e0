<!--
 * @Description: 问卷管理
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 20:09:36
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content mr-20px ml-20px">
        <Spin :spinning="loading" size="large">
          <div class="point">认定过程</div>
          <div class="fw-tip">请选择困难生类型的生成依据，更换选择后需要重新编辑困难生类型</div>
          <a-radio-group v-model:value="type" style="display: flex; flex-direction: column">
            <a-radio :value="item.value" v-for="item in options" class="mb-10px" :key="item.value">
              {{ item.label }}<br />
              <span class="remark">{{ item.remark }}</span>
            </a-radio>
          </a-radio-group>
          <div class="point mt-20px"
            >困难生{{ type == 3 ? '等级' : '类型' }}设置

            <a-button @click="handleAddOrUpdate" type="primary" size="small" class="ml-10px">新增</a-button>
          </div>
          <div class="fw-tip"> 已创建3个困难生类型</div>
          <!-- 页面搜索区 -->
          <div class="fw-tip">
            <Mtcn-Input v-model="keyWord" placeholder="请输入关键词搜索" />
            <a-button @click="fetchData" type="primary">搜索</a-button>
            <a-button @click="handleResset">重置</a-button>
          </div>
          <div class="portalMain">
            <template v-for="(item, index) in batchList" :key="index">
              <div :class="['card', item.isUsed ? 'cardSel' : '']">
                <div class="cardTop">
                  <!-- 卡片标题区 -->
                  <p class="cardTitle">{{ item.name }}</p>
                  <!-- 卡片详情描述区 -->
                  <p class="cardTemplate" v-if="type == 1"> {{ item.approvalStatus }} </p>
                  <p class="cardTemplate" v-if="type == 2"> 人均年收入自动评定：未设置 </p>
                  <p class="cardTemplate" v-if="type == 3"> 问卷填写模式 </p>
                  <p class="cardTemplate"> 资助目标：{{ item.aidTarget }} </p>
                </div>
                <!-- 卡片按钮组区 -->
                <div class="cardOpts">
                  <p class="link" @click="handleAddOrUpdate(item)">编辑</p>
                  <div class="split"></div>
                  <p class="link" @click="handleDelete(item)">删除</p>
                  <div class="split"></div>
                  <a-switch v-model:checked="item.isUsed" checked-children="启用" un-checked-children="停用"></a-switch>
                </div>
              </div>
            </template>
          </div>
        </Spin>
      </div>
    </div>
    <Form @register="registerModal" @reload="" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useModal } from '@/components/Modal';
  import { Spin, Empty } from 'ant-design-vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import Form from './Form.vue';
  const type = ref('1');
  const options = [
    {
      label: '审核认定',
      value: '1',
      remark: '注：有学生在填写申请表时自主选择',
    },
    {
      label: '人均年收入自动评定',
      value: '2',
      remark: '注：根据学生填写的家庭人均年收入认定困难生类型',
    },
    {
      label: '问卷填写模式',
      value: '3',
      remark: '注：学生申请困难生时需要填写问卷，管理员根据问卷得分设置困难生类型',
    },
  ];
  const batchList = ref([
    {
      id: '1',
      name: '一般困难',
      approvalStatus: '审核认定',
      aidTarget: '2500',
      isUsed: true,
    },
    {
      id: '2',
      name: '困难',
      approvalStatus: '审核认定',
      aidTarget: '5500',
      isUsed: false,
    },
    {
      id: '3',
      name: '特殊困难',
      approvalStatus: '审核认定',
      aidTarget: '7500',
      isUsed: true,
    },
  ]);
  const loading = ref(false);
  const keyWord = ref('');
  function handleAddOrUpdate(record) {
    openModal(true, { ...record, type: type.value });
  }
  function handleDelete(record) {}
  function handleResset() {
    keyWord.value = '';
    fetchData();
  }
  function fetchData() {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
  const [registerModal, { openModal }] = useModal();
  onMounted(() => {});
</script>
<style lang="less" scoped>
  .mtcn-content-wrapper-content {
    background: #fff;
    position: relative;

    overflow: scroll !important;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .fw-tip {
    margin: 10px 0;
    color: #666666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    .ant-input {
      width: 300px;
    }
  }
  .remark {
    color: #666666;
    font-size: 12px;
  }
  .portalMain {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

    margin: 20px 0;
    grid-gap: 10px;
    .card {
      border: 1px solid #edeff2;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
      .cardTop {
        padding: 16px;
        .cardTitle {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          font-size: 16px;
          // font-weight: 700;
          margin-bottom: 16px;
        }
        .cardTemplate {
          margin-top: 4px;
          color: #86909c;
          font-size: 14px;
          line-height: 22px;
          span {
            color: #4e5969;
          }
        }
      }
      .cardOpts {
        padding: 11px 12px;
        border-top: 1px solid #edeff2;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .link {
          border-color: transparent;
          color: @primary-color;
          background: 0 0;
          display: inline-block;
          line-height: 1;
          white-space: nowrap;
          cursor: pointer;
        }
        .split {
          width: 1px;
          height: 12px;
          margin: 0 12px;
          background-color: #dfe2e8;
          flex-shrink: 0;
        }
      }
    }
    .cardSel::after {
      content: '已启用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #00b42a;
    }
  }
</style>
