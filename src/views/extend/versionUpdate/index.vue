<template>
  <div class="mtcn-content-wrapper version">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <ScrollContainer>
          <div class="version-update-container">
            <div class="version-list">
              <div v-for="(version, index) in versionList" :key="version.id" class="version-item">
                <!-- 版本标题和日期 -->
                <div class="version-header">
                  <h2 class="version-title">
                    {{ version.title }}
                    <a v-if="index == 0" style="color: #1890ff; text-decoration: none; margin-left: 4px; font-size: 15px">
                      <i class="icon-ym icon-ym-extend-rocket"></i>
                      去升级</a
                    >
                  </h2>

                  <div class="version-date">
                    <CalendarOutlined />
                    <span>{{ version.releaseDate }}</span>
                  </div>
                </div>
                <div class="version-detail-num">{{ version.subTitle }}</div>
                <!-- 富文本内容展示 -->
                <div class="version-content" v-html="version.content"></div>
                <hr class="version-hr" />
              </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" v-if="hasMore">
              <a-button @click="loadMore" :loading="loading" size="large" type="primary" ghost> 加载更多版本 </a-button>
            </div>
          </div>
        </ScrollContainer>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { CalendarOutlined } from '@ant-design/icons-vue';
  import { getNoticeList } from '@/api/system/version';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ScrollContainer } from '@/components/Container';

  defineOptions({ name: 'extend-version-update' });

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const hasMore = ref(true);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 版本列表数据
  const versionList = ref([
    {
      id: 1,
      title: '本次版本更新【3.5.0GAu6】',
      subTitle: '3.5.0GAu6 加Q群:2155613 （windows 版本无压测及CI CD功能）',
      version: '3.5.0GAu6',
      releaseDate: '2025-07-04',
      content: `
        <div style=" padding: 16px; border-radius: 4px; background-color: #fff; margin: 0;">
          

          <p style="margin: 12px 0 4px 0; font-size: 14px; font-weight: normal; color: #333;">6个增强/更新</p>
          <ol style="margin: 0 0 12px 0; padding-left: 16px; font-size: 14px; line-height: 1.4; color: #333;">
            <li style="margin-bottom: 2px;">需求，缺陷和任务：可复制共享URL，点击复制共享的URL登录后自动显示共享的事项</li>
            <li style="margin-bottom: 2px;">项目文档：共享文档页面，显示共享时备注信息</li>
            <li style="margin-bottom: 2px;">接口测试：建场页面，可以直接建执行版本</li>
            <li style="margin-bottom: 2px;">管理中心：人员工作跟踪中过滤掉假删项目</li>
            <li style="margin-bottom: 2px;">项目管理：阶段甘特图双击下钻到迭代甘特图时，窗口标题中带上迭代名</li>
            <li style="margin-bottom: 2px;">手册：官网下载页增加安装FAQ</li>
          </ol>

          <p style="margin: 12px 0 4px 0; font-size: 14px; font-weight: normal; color: #333;">5个Bug修复</p>
          <ol style="margin: 0; padding-left: 16px; font-size: 14px; line-height: 1.4; color: #333;">
            <li style="margin-bottom: 2px;">项目文档：全局视图创建的目录，查看不了上传的文档</li>
            <li style="margin-bottom: 2px;">管理中心：工员工作跟踪，在特定情况下查询不出数据</li>
            <li style="margin-bottom: 2px;">迭代进度计划：特定情况下计算进度时，统计的数据有误</li>
            <li style="margin-bottom: 2px;">管理中心：3.5引入的BUG，管理人员设置缺省不显示所有项目的统计，但还是还是了所有项目</li>
            <li style="margin-bottom: 2px;">文档共享：当内外网访问地址不一致时，外网访问共享链接，访问不了文档，登录后才能访问</li>
          </ol>
        </div>
      `,
    },
    {
      id: 2,
      title: '本次版本更新【3.5.0GAu5】',
      subTitle: '3.5.0GAu5 加Q群:2155613 （windows 版本无压测及CI CD功能）',
      version: '3.5.0GAu5',
      releaseDate: '2025-06-24',
      content: `
        <div style=" padding: 16px; border-radius: 4px; background-color: #fff; margin: 0;">
          

          <p style="margin: 12px 0 4px 0; font-size: 14px; font-weight: normal; color: #333;">2个增强/更新</p>
          <ol style="margin: 0 0 12px 0; padding-left: 16px; font-size: 14px; line-height: 1.4; color: #333;">
            <li style="margin-bottom: 2px;">任务管理：状态和人员改为多选</li>
            <li style="margin-bottom: 2px;">项目文档：优化Zip包导入</li>
          </ol>

          <p style="margin: 12px 0 4px 0; font-size: 14px; font-weight: normal; color: #333;">2个Bug修复</p>
          <ol style="margin: 0; padding-left: 16px; font-size: 14px; line-height: 1.4; color: #333;">
            <li style="margin-bottom: 2px;">项目文档：zip包导入文档，有时候文档查询是不显示</li>
            <li style="margin-bottom: 2px;">windows版安装包：目录检查提示不对，虽然不影响安装</li>
          </ol>
        </div>
      `,
    },
    {
      id: 3,
      title: '版本更新【1.5.0RC12u1】',
      subTitle: '1.5.0RC12u1 加Q群:2155613 （windows 版本无压测及CI CD功能）',
      version: '1.5.0RC12u1',
      releaseDate: '2024-01-23',
      content: `
        <div style=" padding: 16px; border-radius: 8px; background-color: #fff8f6; margin-bottom: 16px;">
          <h3 style="color: #333; margin-top: 20px; margin-bottom: 12px;">2个安全升级</h3>
          <ol style="padding-left: 20px; line-height: 1.8;">
            <li>tomcat 升级到 9.0.85 解决中危(CVE-2023-41080)漏洞</li>
            <li>用户登录时传后台数据加密</li>
          </ol>
        </div>
      `,
    },
  ]);

  // 获取时间线颜色
  const getTimelineColor = (index: number) => {
    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
    return colors[index % colors.length];
  };

  // 加载版本数据
  const loadVersions = async () => {
    try {
      loading.value = true;
      const params = {
        currentPage: currentPage.value,
        pageSize: pageSize.value,
      };

      const result = await getNoticeList(params);

      if (result && result.data && result.data.list) {
        // 处理API返回的数据，转换为前端需要的格式
        const newVersions = result.data.list.map(item => ({
          id: item.id,
          title: item.title || `版本更新【${item.version}】`,
          version: item.version,
          releaseDate: item.releaseDate,
          isLts: item.isLts || false,
          qqGroup: item.qqGroup || '2155613',
          note: item.note,
          downloadUrl: item.downloadUrl,
          enhancements: item.enhancements ? item.enhancements.split('\n').filter(Boolean) : [],
          bugFixes: item.bugFixes ? item.bugFixes.split('\n').filter(Boolean) : [],
          securityUpdates: item.securityUpdates ? item.securityUpdates.split('\n').filter(Boolean) : [],
        }));

        if (currentPage.value === 1) {
          versionList.value = newVersions;
        } else {
          versionList.value.push(...newVersions);
        }

        hasMore.value = result.data.list.length === pageSize.value;
      }
    } catch (error) {
      console.error('加载版本数据失败:', error);
      createMessage.error('加载版本数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 加载更多
  const loadMore = () => {
    currentPage.value++;
    loadVersions();
  };

  // 组件挂载时加载数据
  onMounted(() => {
    // loadVersions(); // 暂时注释掉API调用，使用模拟数据
  });
</script>

<style lang="less" scoped>
  .version {
    background-image: linear-gradient(0deg, #ffffff 0%, #e5f1ff 100%);
    background-size: 100% 500px;
    background-repeat: no-repeat;
    .version-update-container {
      margin: 60px auto 116px;
      width: 70vw;

      min-height: 100vh;
      padding: 50px 40px;
      background: #ffffff;
      border-top: 4px solid #007aff;
      border-radius: 4px;
      -webkit-box-shadow: 0 3px 20px 0 rgba(0, 32, 80, 0.08);
      box-shadow: 0 3px 20px 0 rgba(0, 32, 80, 0.08);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      .version-list {
        width: 100%;
        .version-item {
          margin-bottom: 40px;

          &:last-child {
            margin-bottom: 0;
          }

          .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px dashed #ced2d8;

            .version-title {
              font-size: 28px;
              font-weight: 600;
              color: #111926;
              margin: 0;
            }

            .version-date {
              display: flex;
              align-items: center;
              color: #999;
              font-size: 14px;

              .anticon {
                margin-right: 4px;
              }
            }
          }
          .version-detail-num {
            margin-top: 20px;
            font-family: PingFangSC-Medium;
            font-size: 16px;
            font-weight: 600;
            color: #111926;
            letter-spacing: 0;
            line-height: 16px;
          }
          .version-content {
            :deep(div) {
              padding: 16px !important;
              border-radius: 4px !important;
              background-color: #fff !important;
              margin: 0 !important;
              font-size: 14px;
              line-height: 1.4;
              color: #333;
            }

            :deep(p) {
              margin: 0 0 12px 0 !important;
              font-size: 14px !important;
              line-height: 1.4 !important;
              color: #333 !important;
              font-weight: normal !important;
            }

            :deep(ol) {
              margin: 0 0 12px 0 !important;
              padding-left: 16px !important;
              font-size: 14px !important;
              line-height: 1.4 !important;
              color: #333 !important;
            }

            :deep(li) {
              margin-bottom: 2px !important;
              font-size: 14px !important;
              line-height: 1.4 !important;
              color: #333 !important;
            }

            :deep(a) {
              color: #1890ff !important;
              text-decoration: none !important;
              margin-left: 4px !important;
            }
          }
        }
        .version-hr {
          margin-top: 26px;
          margin-bottom: 50px;
          width: 100%;
          height: 2px;
          background: #e8ebf0;
          border: 0;
        }
      }

      .load-more {
        width: 100%;
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
      }
    }
  }
</style>
