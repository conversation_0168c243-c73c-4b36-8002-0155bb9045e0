<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">{{
              t('common.addText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, inject } from 'vue';
import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@/hooks/web/useMessage';
import { useRoute } from 'vue-router';
import Form from './exportTemplate/Form.vue';
const { t } = useI18n();
const route = useRoute();
const { createMessage, createConfirm } = useMessage();
const useBaseApi = inject('useBaseApi');
const exportTemplateApi = useBaseApi('/api/export/config');
const columns: BasicColumn[] = [
  { title: '功能编码', dataIndex: 'exportType' },
  { title: '功能名称', dataIndex: 'exportName' },
  { title: '实体类名', dataIndex: 'entityName', width: 500 },
  // { title: '创建人', dataIndex: 'enCode', width: 200 },
  // { title: '创建时间', dataIndex: 'enCode', width: 200 },
];
const [registerForm, { openModal: openFormModal }] = useModal();
const [registerTable, { reload, getForm }] = useTable({
  api: (params) => exportTemplateApi.request('get', '/getList', {
    data: params
  }),
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        field: 'keyword',
        label: t('common.keyword'),
        component: 'Input',
        componentProps: {
          placeholder: '请输入功能编码/功能名称/实体类名搜索',
          submitOnPressEnter: true,
        },
      },

    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
  beforeFetch: data => {
    if (route.query?.appId) data.localId = route.query?.appId || '';
    return data;
  },
});

function getTableActions(record): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id),
    },
    {
      label: t('common.delText'),
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}
function addOrUpdateHandle(id = '') {
  openFormModal(true, { id, lowCodeAppId: route.query?.appId });
}
function handleExport(id) {
  exportTpl(id).then(res => {
    downloadByUrl({ url: res.data.url });
  });
}
function handleDelete(id) {
  exportTemplateApi.request('delete', `/remove/${id}`).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

onMounted(() => {

});
</script>
