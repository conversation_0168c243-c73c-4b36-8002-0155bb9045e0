<template>
  <BasicModal :width="800" v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn @ok="handleSubmit"
    destroyOnClose>
    <BasicForm @register="registerForm">
      <template #fieldList>
        <template v-if="Object.keys(columnList).length > 0">
          <a-form-item-rest>
            <a-checkbox v-model:checked="checkAll" @change="handleCheckAllChange">全选</a-checkbox><br />
          </a-form-item-rest>
          <a-checkbox-group
            :value="Object.keys(checkedList)"
            class="options-list mt-10px"
            @change="handleCheckedChange"
          >
            <a-checkbox v-for="(item,key) in columnList" :key="key" :value="key" class="options-item">
              {{ item }}
            </a-checkbox>
          </a-checkbox-group>
        </template>
        <template v-else>
          <Empty :image="simpleImage" v-if="!Object.keys(columnList).length" />
        </template>
      </template>
    </BasicForm>

  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, unref, computed, reactive,inject } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm, FormSchema } from '@/components/Form';
import { useMessage } from '@/hooks/web/useMessage';
import { useI18n } from '@/hooks/web/useI18n';
import { Empty } from 'ant-design-vue';
import { useBaseStore } from '@/store/modules/base';
import { useRoute } from 'vue-router';
import { getMenuList } from '@/api/tuPass/menu';

const route = useRoute();
const baseStore = useBaseStore();
const useBaseApi = inject('useBaseApi');
const exportTemplateApi = useBaseApi('/api/export/config');


// 响应式数据声明
const simpleImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);
const id = ref('');
const checkAll = ref(false);
// checkedList保存选中的键值对对象，key为字段名，value为显示名
const checkedList = ref({});

const columnList = ref({});


// 表单配置
const schemas: FormSchema[] = [
  {
    field: 'exportType',
    label: '功能编码',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
    colProps: { span: 12 },
  },
  {
    field: 'exportName',
    label: '功能名称',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请输入',
      onChange: (value,obj,extra) => {
        setFieldsValue({
          exportName:obj.fullName
        })
      }
    },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
    colProps: { span: 12 },
  },
  {
    field: 'entityName',
    label: '实体类名',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
    colProps: { span: 20 },
  },
  {
    field: 'btn',
    label: '',
    component: 'Button',
    componentProps: { type: 'primary', buttonText: '查询',onClick:handleSearch },
    colProps: { span: 4 },
  },
  {
    field: 'operationPermissions',
    label: '操作权限',
    component: 'Select',
    componentProps: { placeholder: '请选择' },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
    colProps: { span: 12 },
    ifShow: (values) => {
      return Object.keys(columnList.value).length > 0
    }
  },
  {
    field: 'fieldList',
    label: '',
    component: 'Input',
    slot: 'fieldList',
    colProps: { span: 24 },
  },
];

// 计算属性和组合式API
const getTitle = computed(() => (!unref(id) ? t('common.addText') : t('common.editText')));
const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const { t } = useI18n();

// 表单和模态框注册
const [registerForm, { setFieldsValue, validate, resetFields, updateSchema }] = useForm({ schemas: schemas });
const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);

/**
 * 初始化模态框数据
 * @param data 传入的数据对象
 */
function init(data) {
  // 先清空复选框相关状态
  checkedList.value = {};
  checkAll.value = false;
  columnList.value = {};
  resetFields();
  getOptions();
  id.value = data?.id;
  if(id.value){
    getDetail(id.value)
  }
}
async function getDetail(id){
  // 调用API获取详情数据
  const {data} = await exportTemplateApi.request('get',`/${id}`)
  setFieldsValue(data)

  // 设置所有可选字段（复选框列表）
  columnList.value = data.entityFields || {};

  // 设置已选字段（已勾选的复选框）
  checkedList.value = data.fieldsMap ? JSON.parse(data.fieldsMap) : {};

  // 判断是否全选，若已选字段数量等于所有字段数量则勾选全选
  checkAll.value = Object.keys(checkedList.value).length === Object.keys(columnList.value).length;

  // 禁用部分表单项，隐藏查询按钮
  updateSchema([
    {
      field:'entityName',
      componentProps:{ disabled:true }
    },
    {
      field:'exportName',
      componentProps:{ disabled:true }
    },
    {
      field:'exportType',
      componentProps:{ disabled:true }
    },
    {
      field:'btn',
      ifShow:false
    }
  ])
}
async function handleSearch(){
  const values = await validate();
  if (!values) return;
  const query = {
    exportType:values.exportType,
    exportName:values.exportName,
    entityName:values.entityName,
    localId:route.query.appId
  }
  exportTemplateApi.request('get','/getInfoByType',{
    data:query
  }).then(res=>{
    for(let i in res.data){
      columnList.value[i] = res.data[i]
    }
  })
}
/**
 * 处理全选复选框变化
 * @param e 事件对象
 */
function handleCheckAllChange(e) {
  // 全选时，checkedList为columnList的完整拷贝，否则为空对象
  checkedList.value = e.target.checked ? { ...columnList.value } : {};
  // 同步checkAll状态
  checkAll.value = e.target.checked;
}

/**
 * 处理复选框组变化
 * @param value 选中的key数组
 */
function handleCheckedChange(value) {
  // value为选中的key数组，生成键值对对象
  checkedList.value = value.reduce((obj, key) => {
    obj[key] = columnList.value[key];
    return obj;
  }, {});
  // 判断是否全选
  checkAll.value = value.length === Object.keys(columnList.value).length;
}

/**
 * 获取选项数据
 */
 async function getOptions() {
  const [dictRes, menuRes] = await Promise.all([
      baseStore.getDictionaryData('rzjb'),
      getMenuList({
        lowCodeAppId: route.query.appId,
        category: 'Web'
      })
    ]);

    updateSchema([
      {
        field: 'operationPermissions',
        componentProps: {
          options: [{
            fullName: '所有',
            enCode: '-1'
          }, ...(dictRes || [])],
          fieldNames: { label: 'fullName', value: 'enCode' }
        }
      },
      {
        field: 'exportName',
        componentProps: {
          options: menuRes.data?.list || [],
          fieldNames: { label: 'fullName', value: 'id' }
        }
      }
    ]);
}

/**
 * 处理表单提交
 */
async function handleSubmit() {
  const values = await validate();
    if (!values) return;

    const query={
      entityName:values.entityName,
      exportName:values.exportName,
      exportType:values.exportType,
      localId:route.query.appId,
      operationPermissions:values.operationPermissions,
      fieldsMap:JSON.stringify(checkedList.value)
    }
    changeOkLoading(true);
    const method = id.value ? 'put' : 'post';
    const url = id.value ? `/edit/${id.value}` : '/save';
    exportTemplateApi.request(method,url,{
      data:query
    })
      .then(res => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closeModal();
        emit('reload');
      })
      .catch(() => {
        changeOkLoading(false);
      });
}
</script>
