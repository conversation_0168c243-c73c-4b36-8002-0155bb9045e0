<template>
  <BasicPopup v-bind="$attrs" title="公示模板设置" showOkBtn okText="保存" cancelText="关闭" @register="registerPopup" class="full-popup" @close="handleClose">
    <div class="setting-box p-20px">
      <div class="left-box">
        <div class="flex">
          <div class="point mr-10px">预览</div>
          <a-radio-group size="small" v-model:value="selectedOption" button-style="solid">
            <a-radio-button value="1">列表式</a-radio-button>
            <a-radio-button value="2">文本式</a-radio-button>
          </a-radio-group>
        </div>
        <h2 class="title mt-10px">困难生公示</h2>
        <hr class="mt-10px mb-10px" />
        <div class="sub-title mb-20px">
          <div v-if="fieldValue.assessmentCount">获奖人数：10人</div>
          <div v-if="fieldValue.publicTime">公示日期：2025.05.01~2025.10.01</div>
          <div v-if="fieldValue.viewCount">浏览次数：100次</div>
        </div>
        <BasicTable @register="registerTable" v-if="selectedOption == 1"> </BasicTable>
        <template v-else>
          <div class="content-box mt-20px">
            <div class="text-content"> 一等奖 <span>2人</span> 获奖 </div>
            <div class="text-content"> 某某学院 <span>2人</span> 获奖 </div>
            <div class="flex name-list">
              <div>杨宇(100000001)</div>
              <div>张三(100000002)</div>
            </div>
          </div>
        </template>
      </div>
      <div class="right-box">
        <div class="point mb-10px">公示页面信息设置</div>
        <BasicForm @register="registerForm"> </BasicForm>
      </div>
    </div>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, nextTick, watch, unref } from 'vue';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ScrollContainer } from '@/components/Container';
  const { createMessage, createConfirm } = useMessage();
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const selectedOption = ref(1);
  const fieldValue = ref({
    assessmentCount: 1,
    publicTime: 1,
    viewCount: 1,
    nrxs: ['xm', 'xh', 'pddj', 'sy', 'xy', 'zy', 'bj'],
  });

  const columns: BasicColumn[] = [
    {
      title: '困难等级',
      dataIndex: 'pddj',
      width: 100,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('pddj');
      },
    },
    {
      title: '学院',
      dataIndex: 'xy',
      width: 150,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('xy');
      },
    },
    {
      title: '学号',
      dataIndex: 'xh',
      width: 120,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('xh');
      },
    },
    {
      title: '姓名',
      dataIndex: 'xm',
      width: 100,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('xm');
      },
    },
    // {
    //   title: '书院',
    //   dataIndex: 'sy',
    //   width: 100,
    //   resizable: true,
    //   ellipsis: true,
    //   ifShow: ({ row }) => {
    //     return fieldValue.value?.nrxs?.includes('sy');
    //   },
    // },
    {
      title: '学院',
      dataIndex: 'xy',
      width: 100,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('xy');
      },
    },
    {
      title: '专业',
      dataIndex: 'zy',
      width: 100,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('zy');
      },
    },
    {
      title: '班级',
      dataIndex: 'bj',
      width: 100,
      resizable: true,
      ellipsis: true,
      ifShow: ({ row }) => {
        return fieldValue.value?.nrxs?.includes('bj');
      },
    },
  ];
  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    dataSource: [
      { pddj: '一等奖', xy: '某某学院', xh: '01220901', xm: '杨宇' },
      { pddj: '一等奖', xy: '某某学院', xh: '01220901', xm: '杨宇' },
      { pddj: '一等奖', xy: '某某学院', xh: '01220901', xm: '杨宇' },
      { pddj: '一等奖', xy: '某某学院', xh: '01220901', xm: '杨宇' },
      { pddj: '一等奖', xy: '某某学院', xh: '01220901', xm: '杨宇' },
    ],
    columns,
    showTableSetting: false,
  });
  const [registerForm, { setFieldsValue, resetFields, validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'nrxs',
        label: '内容显示',
        component: 'Checkbox',
        componentProps: {
          options: [
            {
              fullName: '姓名',
              id: 'xm',
            },
            {
              fullName: '学号',
              id: 'xh',
            },
            {
              fullName: '困难等级',
              id: 'pddj',
            },
            // {
            //   fullName: '书院',
            //   id: 'sy',
            // },
            {
              fullName: '学院',
              id: 'xy',
            },
            {
              fullName: '专业',
              id: 'zy',
            },
            {
              fullName: '班级',
              id: 'bj',
            },
          ],
          onchange: async value => {
            fieldValue.value = await getFieldsValue();
          },
        },
      },
      {
        field: 'viewCount',
        label: '浏览次数',
        component: 'Switch',
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onclick: async value => {
            fieldValue.value = await getFieldsValue();
          },
        },
      },
      {
        field: 'assessmentCount',
        label: '评定人数',
        component: 'Switch',
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onclick: async value => {
            fieldValue.value = await getFieldsValue();
          },
        },
      },
      {
        field: 'publicTime',
        label: '公示日期',
        component: 'Switch',
        defaultValue: 1,
        componentProps: {
          checkedChildren: '打开',
          unCheckedChildren: '关闭',
          onclick: async value => {
            fieldValue.value = await getFieldsValue();
          },
        },
      },
    ],
    layout: 'vertical',
  });
  const emit = defineEmits(['register', 'reload']);

  async function init(data) {
    setFieldsValue(fieldValue.value);
  }
  const handleClose = () => {
    closePopup();
  };
</script>
<style scoped lang="less">
  .setting-box {
    height: 100%;
    display: grid;
    grid-template-columns: minmax(100px, 1fr) 400px;
    gap: 20px;

    .right-box {
      padding: 0 20px;
      border-left: 1px solid #dddddd;
    }
    .left-box {
      height: calc(100% - 40px);
      .content-box {
        display: flex;
        flex-direction: column;
        gap: 20px;
        .text-content {
          span {
            font-size: 18px;
            color: red;
          }
        }
        .name-list {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          gap: 10px;
        }
      }

      .title {
        font-size: 20px;
        font-weight: 500;
        text-align: center;
      }
      .sub-title {
        display: grid;
        grid-template-columns: 1fr 3fr 1fr;
        gap: 20px;
        text-align: center;
      }
    }
  }
</style>
