<!--
 * @Description: 困难生公示
 * @Autor: Fhz
 * @Date: 2025-03-03 11:04:45
 * @LastEditors: Fhz
 * @LastEditTime: 2025-05-06 15:11:44
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-flow-node-system-task" @click="openSettingPopup(true, {})">公示模板设置</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <ExportModal @register="registerExportModal" />
    <SettingPopup @register="registerPopup" />
  </div>
</template>

<script lang="ts" setup>
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { useMessage } from '@/hooks/web/useMessage';
  // import { getList, exportList } from '@/api/studentGrant/studentGrantNotice';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import SettingPopup from './SettingPopup.vue';
  const { createMessage } = useMessage();
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  const [registerPopup, { openPopup: openSettingPopup }] = usePopup();
  const columns: BasicColumn[] = [
    {
      title: '公示标题',
      dataIndex: 'title',
      width: 500,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '公示人数',
      dataIndex: 'numberOfPeople',
      width: 100,
      align: 'right',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '公示时间',
      dataIndex: 'publishDate',
      width: 180,
      format: 'date|YYYY-MM-DD HH:mm:ss',
      resizable: true,
      ellipsis: true,
    },
  ];

  const [registerTable, { reload }] = useTable({
    // api: getList,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
      
        {
          field: 'gsfw',
          label: '公示范围',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
          },
        },
      ],
    },
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    dataSource: [
      {
        id: 1,
        title: '2022-2023学年2022年困难生-国家困难生(学校公示)',
        numberOfPeople: 149,
        publishDate: '2022-04-01 10:00:00',
        status: 'published',
      },
      {
        id: 2,
        title: '2022-2023学年2022年困难生-校外资助困难生(学校公示)',
        numberOfPeople: 1,
        publishDate: '2022-04-01 10:05:00',
        status: 'published',
      },
      {
        id: 3,
        title: '2022-2023学年2022年困难生-新疆少数民族经济困难学生困难生(学校公示)',
        numberOfPeople: 1,
        publishDate: '2022-04-01 10:10:00',
        status: 'published',
      },
      {
        id: 4,
        title: '2022-2023学年2022年困难生-小米困难生(学校公示)',
        numberOfPeople: 1,
        publishDate: '2022-04-01 10:15:00',
        status: 'published',
      },
    ],
  });

  function getTableActions(record): any[] {
    return [
      {
        label: '导出名单',
        onClick: () => handleExport(record.id),
      },
    ];
  }

  function addOrUpdateHandle() {
    openFormModal(true);
  }

  function handleExport() {
    openExportModal(true);
  }
</script>
