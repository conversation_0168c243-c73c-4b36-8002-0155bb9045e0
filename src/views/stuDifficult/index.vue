<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #headerTop>
            <Mtcn-Alert showIcon :message="`您目前还不是困难生！申请时间为：2025-04-23 ~ 2025-05-01`"> </Mtcn-Alert>
          </template>
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button type="primary" @click="addOrUpdateHandle()">申请</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 表格列插槽区 -->
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === '待审核' ? '' : record.status === '已通过' ? 'green' : 'red'">
                {{ record.status }}
              </a-tag>
            </template>

            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 申请 -->
    <AddForm @register="registerForm" @reload="reload" />
    <QuestionForm @register="registerQuestionForm" @reload="reload" @finish="handleQuestionFinish" />
  </div>
</template>

<script lang="ts" setup>
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { useMessage } from '@/hooks/web/useMessage';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import AddForm from './components/AddForm.vue';
  import QuestionForm from './components/QuestionForm.vue';
  const { createMessage } = useMessage();

  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerQuestionForm, { openPopup: openQuestionFormPopup }] = usePopup();
  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '姓名',
      dataIndex: 'xm',
      width: 80,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学号',
      dataIndex: 'xsbh',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学年',
      dataIndex: 'academicYear',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '困难生类型',
      dataIndex: 'kns',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '申请时间',
      dataIndex: 'applicationTime',
      width: 150,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '院系',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
  ];

  // 注册表格
  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    // api: getUserList,
    columns,
    useSearchForm: true,
    formConfig: getFormConfig(),
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    dataSource: [
      {
        xm: '铁蒙宇',
        xsbh: '2020000001',
        academicYear: '2024-2025',
        kns: '家庭经济困难',
        applicationTime: '2024-09-15',
        status: '未通过',
      },
      {
        xm: '铁蒙宇',
        xsbh: '2020000001',
        academicYear: '2023-2024',
        kns: '突发困难',
        applicationTime: '2023-11-08',
        status: '待审核',
      },
      {
        xm: '铁蒙宇',
        xsbh: '2020000001',
        academicYear: '2022-2023',
        kns: '学习困难',
        applicationTime: '2022-07-20',
        status: '已通过',
      },
    ],
  });

  function getFormConfig(): Partial<FormProps> {
    return {
      schemas: [
        {
          field: 'academicYear',
          label: '学年',
          component: 'Select',
          componentProps: {
            placeholder: '请选择学年',
            submitOnPressEnter: true,
          },
        },
      ],
    };
  }

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
      {
        label: '删除',
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  function handleDelete(id) {
    // delUser(id).then(res => {
    //   createMessage.success(res.msg);
    //   reload();
    // });
  }
  const applyType = ref(3);
  function addOrUpdateHandle(id = undefined) {
    if (applyType.value == 3) {
      openQuestionFormPopup(true, { id });
    } else {
      openFormPopup(true, { id });
    }
  }
  function handleQuestionFinish() {
    openFormPopup(true, {});
  }
</script>
