<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="问卷调查" @ok="handleSubmit" showOkBtn cancelText="关闭" okText="保存" :width="800">
    <Questionnaire ref="questionnaire" :opType="opType" v-if="opType"/>
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  import Questionnaire from './Questionnaire.vue';
  const emit = defineEmits(['register', 'reload', 'finish']);

  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const id = ref('');
  const opType = ref('');
  function init(data) {
    changeLoading(true);
    console.log(data.opType);
    id.value = data.id;
    opType.value = data.opType ? data.opType : 1;
    changeLoading(false);
  }
  const questionnaire = ref();
  async function handleSubmit() {
    changeOkLoading(true);
    const values = await questionnaire.value?.handleSubmit();
    closePopup();
    emit('finish');
    changeOkLoading(false);
  }
</script>
