<!--
 * @Description: 扩展信息表单组件
 * @Autor: Fhz
 * @Date: 2025-02-24 11:21:08
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-23 11:51:55
-->
<template>
  <Spin :spinning="loading" size="large">
    <BasicForm @register="registerForm" class="form-con"> </BasicForm>
  </Spin>
</template>
<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useModal } from '@/components/Modal';
  import { useBaseStore } from '@/store/modules/base';
  import { Empty, Spin } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  const { createMessage } = useMessage();
  const props = defineProps({
    formData: Array,
    name: String,
    index: Number,
    operationType: Number,
  });
  const baseStore = useBaseStore();
  const schemas: FormSchema[] = ref([]);
  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields, updateSchema }] = useForm({ schemas: schemas, labelWidth: '130px' });
  const loading = ref(false);
  async function initForm(data) {
    addSchema(
      'groupTitleField',
      '',
      'GroupTitle',
      false,
      { sm: 24 },
      {
        content: `${props.name} - ${props.index}`,
        btnList: [
          {
            name: '删除',
            type: 'link',
          },
        ],
        onClick(e) {
          handleDelete();
        },
      },
    );
    addSchema('id', '', 'Input', false, { sm: 24 }, {});
    for (const item of data) {
      let obj = {};
      if (item.msmfSource) {
        const res = await baseStore.getDictionaryData(item.msmfSource);
        obj = {
          options: res,
          fieldNames: { label: 'fullName', value: 'enCode' },
        };
      }
      if (item.msmfType === 'UploadFile') {
        setFieldsValue({ [item.msmfEname]: [] });
      }

      addSchema(
        item.msmfEname,
        item.msmfName,
        item.msmfType,
        item.msmfIsrequired,
        { sm: 12 },
        { placeholder: item.msmfName, ...item.componentProps, ...obj, readonly: props.operationType == 0 },
      );
      updateSchema([
        {
          field: 'id',
          ifShow: false,
        },
      ]);
    }
  }
  function addSchema(field, label, component, required, colProps, componentProps = {}, slot) {
    schemas.value.push({
      field,
      label,
      component,
      required: required == '1',
      colProps: { sm: 24, ...colProps },
      componentProps,
      slot,
    });
  }
  const emit = defineEmits(['reload', 'del']);
  function handleDelete() {
    const value = getFieldsValue();
    emit('del', props.index);
  }
  async function handleValidate() {
    const values = await validate();
    if (values) {
      for (const field of props.formData) {
        if (field.msmfType == 'UploadFile') {
          values[field.msmfEname] = values[field.msmfEname] ? JSON.stringify(values[field.msmfEname]) : '';
        }
        values[field.msmfEname] = formatDate(values[field.msmfEname], field);
      }
    }
    return values;
  }
  function formatDate(value, field) {
    if (field.msmfType === 'DatePicker') {
      return field.msmfEname === 'ksny' || field.msmfEname === 'jsny' ? dayjs(value).format('YYYY-MM') : dayjs(value).format('YYYY-MM-DD');
    }
    return value;
  }
  defineExpose({ setFieldsValue, getFieldsValue, handleValidate, resetFields });
  onMounted(() => {
    initForm(props.formData);
  });
</script>
<style scoped>
  .ant-form .ant-form-item .btn {
    float: right;
  }
</style>
