<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="困难生申请" @ok="handleSubmit" :width="800" showOkBtn cancelText="关闭" okText="保存">
    <BasicForm @register="registerForm">
      <template #cyqk>
        <div v-for="item in formArr" class="m-l-10px m-r-10px">
          <ExtendForm @del="handleDelForm" :formData="familyMembersForm" :ref="el => setFormRef(el, item.index)" :index="item.index" name="家庭成员情况" />
        </div>
      </template>
    </BasicForm>
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useI18n } from '@/hooks/web/useI18n';
  import ExtendForm from './ExtendForm.vue';
  // import { createDictionaryData as create, updateDictionaryData as update, getDictionaryDataInfo as getInfo } from '@/api/systemData/dictionary';
  const emit = defineEmits(['register', 'reload']);
  // #region 家庭成员情况扩展信息
  const formRefs = reactive<any>({});
  const formArr = ref<any[]>([]);
  const familyMembersForm = reactive([
    {
      msmfSource: '',
      msmfEname: 'name',
      msmfIsrequired: '1',
      msmfType: 'Input',
      msmfName: '姓名',
      msmfValue: '',
    },
    {
      msmfSource: '',
      msmfEname: 'birthdate',
      msmfIsrequired: '1',
      msmfType: 'DatePicker',
      msmfName: '出生日期',
      msmfValue: '',
    },
    {
      msmfSource: '',
      msmfEname: 'relation',
      msmfIsrequired: '1',
      msmfType: 'Select',
      msmfName: '亲属关系',
      msmfValue: '',
    },
    {
      msmfSource: '',
      msmfEname: 'occupation',
      msmfIsrequired: '0',
      msmfType: 'Input',
      msmfName: '职业',
      msmfValue: '',
    },
    {
      msmfSource: '',
      msmfEname: 'workUnit',
      msmfIsrequired: '0',
      msmfType: 'Input',
      msmfName: '工作单位',
      msmfValue: '',
    },
    {
      msmfSource: '',
      msmfEname: 'averageIncome',
      msmfIsrequired: '0',
      msmfType: 'Input',
      msmfName: '平均月收入',
      msmfValue: '',
    },
    {
      msmfSource: '',
      msmfEname: 'healthStatus',
      msmfIsrequired: '0',
      msmfType: 'Select',
      msmfName: '健康状况',
      msmfValue: '',
    },
  ]);
  function setFormRef(el: any, index: number) {
    if (el) {
      formRefs[`form${index}`] = el;
    }
  }
  function handleDelForm(e: number) {
    formArr.value = formArr.value.filter(item => item.index !== e);
  }
  async function handleValidateFamilyMembers() {
    let values = [];
    for (const item of formArr.value) {
      const formKey = `form${item.index}`;
      const formInstance = formRefs[formKey];
      if (formInstance) {
        const value = await formInstance.handleValidate();
      }
    }

    if (values.length === 0) return;
    return values;
  }
  // #endregion
  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: [
      {
        field: 'studentInfo',
        component: 'GroupTitle',
        componentProps: { content: '学生信息' },
        colProps: { span: 24 },
      },
      {
        field: 'studentId',
        label: '学号',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
        rules: [{ required: true, trigger: 'blur', message: '请输入学号' }],
      },
      {
        field: 'name',
        label: '姓名',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
        rules: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
      },

      {
        field: 'gender',
        label: '性别',
        component: 'Radio',
        componentProps: {
          options: [
            { fullName: '男', id: '是' },
            { fullName: '女', id: '否' },
          ],
          disabled: true,
        },
      },
      {
        field: 'ethnicity',
        label: '民族',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
      },
      {
        field: 'birthPlace',
        label: '生源地',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
      },
      {
        field: 'hkxz',
        label: '入学前户口性质',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
      },
      {
        field: 'dw',
        label: '院系',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
      },
      {
        field: 'currentYear',
        label: '现在年级',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
      },
      {
        field: 'major',
        label: '专业',
        component: 'Input',
        componentProps: { readonly: true, placeholder: '请输入' },
      },
      {
        field: 'familyMembers',
        component: 'GroupTitle',
        componentProps: {
          content: '家庭成员情况',
          btnList: [
            {
              name: '新增',
              type: 'link',
            },
          ],
          onClick: () => {
            formArr.value.push({ index: formArr.value.length + 1 });
          },
        },
        colProps: { span: 24 },
      },
      {
        field: 'cyqk',
        component: 'Input',
        componentProps: { content: '家庭成员情况' },
        slot: 'cyqk',
        colProps: { span: 24 },
      },
      {
        field: 'familySituation',
        component: 'GroupTitle',
        componentProps: { content: '家庭情况' },
        colProps: { span: 24 },
      },
      {
        field: 'address',
        label: '家庭地址',
        component: 'Input',
        componentProps: { placeholder: '请输入' },
        rules: [{ required: true, trigger: 'blur', message: '请输入家庭地址' }],
      },
      {
        field: 'homePhone',
        label: '家庭电话',
        component: 'Input',
        componentProps: { placeholder: '请输入' },
      },
      {
        field: 'homeEmail',
        label: '家庭邮编',
        component: 'Input',
        componentProps: { placeholder: '请输入' },
      },
      {
        field: 'familyType',
        label: '家庭困难类型',
        component: 'Select',
        componentProps: {
          options: [],
        },
      },
      {
        field: 'cjlx',
        label: '残疾类型',
        component: 'Select',
        componentProps: {
          options: [],
        },
      },
      {
        field: 'familyCount',
        label: '家庭人口数',
        component: 'Input',
        rules: [{ required: true, trigger: 'change' }],
      },
      {
        field: 'familyIncome',
        label: '主要收入来源类型',
        component: 'Select',
        componentProps: {
          options: [],
        },
      },
      {
        field: 'familyIncome',
        label: '是否遭受自然灾害',
        component: 'Radio',
        componentProps: {
          options: [
            { fullName: '是', id: '是' },
            { fullName: '否', id: '否' },
          ],
        },
        rules: [{ required: true, trigger: 'change' }],
      },
      {
        field: 'familyIncome',
        label: '是否遭受灾害意外',
        component: 'Radio',
        componentProps: {
          options: [
            { fullName: '是', id: '是' },
            { fullName: '否', id: '否' },
          ],
        },
        rules: [{ required: true, trigger: 'change' }],
      },
      {
        field: 'familyCount',
        label: '赡养人口数',
        component: 'Input',
        rules: [{ required: true, trigger: 'change' }],
      },
      {
        field: 'familyCount',
        label: '劳动力人口数',
        component: 'Input',
        rules: [{ required: true, trigger: 'change' }],
      },
      {
        field: 'hasLoan',
        label: '是否办理贷款',
        component: 'Radio',
        componentProps: {
          options: [
            { fullName: '是', id: '是' },
            { fullName: '否', id: '否' },
          ],
        },
      },
      {
        field: 'otherInfo',
        label: '其他情况',
        component: 'Input',
        componentProps: { placeholder: '请输入' },
      },
      {
        field: 'applyInfo',
        component: 'GroupTitle',
        colProps: { span: 24 },
        componentProps: { content: '申请信息' },
      },
      {
        field: 'applyReason',
        label: '申请陈述',
        component: 'Textarea',
        componentProps: { placeholder: '请输入', rows: 10 },
        rules: [{ required: true, trigger: 'blur' }],
        colProps: { span: 24 },
      },
      {
        field: 'cl',
        label: '佐证材料',
        component: 'UploadFile',
        rules: [{ required: true, trigger: 'blur' }],
        colProps: { span: 24 },
      },
    ],
    labelWidth: 150,
    baseColProps: { span: 12 },
  });
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const id = ref('');
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const baseStore = useBaseStore();

  const getTitle = computed(() => (unref(id) ? '编辑' : '新建'));
  function init(data) {
    changeLoading(true);
    resetFields();
    id.value = data.id;
    // getInfo(id.value).then(res => {
    //   setFieldsValue(res.data);
    //   changeLoading(false);
    // });
    changeLoading(false);
  }

  async function handleSubmit() {
    const familyMembers = await handleValidateFamilyMembers(); //家庭成员情况
    const values = await validate();
    if (!values) return;
    changeOkLoading(true);
    const query = {
      ...values,
      id: id.value,
    };
    // const formMethod = id.value ? update : create;
    // formMethod(query)
    //   .then(res => {
    //     createMessage.success(res.msg);
    //     changeOkLoading(false);
    //     closePopup();
    //     emit('reload');
    //   })
    //   .catch(() => {
    //     changeOkLoading(false);
    //   });
  }
</script>
