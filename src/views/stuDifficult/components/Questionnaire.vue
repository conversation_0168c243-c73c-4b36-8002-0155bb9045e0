<template>
  <div class="p-10px" style="max-width: 600px; margin: 0 auto">
    <Mtcn-Alert v-if="props.opType === 1" showIcon message="必须答完所有题目才能保存哦，请勿中途退出！"> </Mtcn-Alert>

    <BasicForm @register="registerForm" class="m-t-10px"> </BasicForm>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  const props = defineProps({
    opType: {
      type: Number,
      default: 1, //1 学生 2 管理员
    },
  });
  const emit = defineEmits(['register', 'reload']);
  const formArr = ref([]);
  const familyMembersForm = ref({});
  const formRefs = ref({});

  const schemas = ref([
    {
      field: 'score',
      label: '问卷得分',
      component: 'Input',
    },
    {
      field: 'major',
      label: '1、家庭人口数量',
      subLabel: '（单选）',
      component: 'Radio',
      ifShow: true,
      componentProps: {
        options: [
          {
            fullName: '1~2人',
            id: 1,
          },
          {
            fullName: '3~4人',
            id: 2,
          },
          {
            fullName: '5-6人',
            id: 3,
          },
          {
            fullName: '7人以上 ',
            id: 4,
          },
        ],
      },
    },
    {
      field: 'jtcy',
      label: '2、家庭成员',
      subLabel: '（单选）',
      component: 'Checkbox',
      ifShow: true,
      rules: [{ type: 'array' }],
      componentProps: {
        options: [
          {
            fullName: '爸爸',
            id: 1,
          },
          {
            fullName: '妈妈',
            id: 2,
          },
          {
            fullName: '爷爷',
            id: 3,
          },
          {
            fullName: '奶奶 ',
            id: 4,
          },
        ],

        onChange: e => {
          handleAnswer(2, e);
        },
      },
    },
    {
      field: 'sqcs',
      label: '3、请简述自己的家庭状况及申请陈述。',
      subLabel: '（简答）',
      component: 'Textarea',
      ifShow: true,
      componentProps: {
        placeholder: '请输入',
      },
    },

    {
      field: 'houseType',
      label: '4、你家的住房类型是？',
      subLabel: '（单选）',
      component: 'Radio',
      ifShow: true,
      componentProps: {
        options: [
          {
            fullName: '自购房',
            id: 1,
          },
          {
            fullName: '租赁房',
            id: 2,
          },
          {
            fullName: '单位分配房',
            id: 3,
          },
          {
            fullName: '其他',
            id: 4,
          },
        ],
      },
    },
    {
      field: 'familyIncomeSources',
      label: '5、家庭收入来源有哪些？',
      subLabel: '（单选）',
      component: 'Checkbox',
      ifShow: true,
      rules: [{ type: 'array' }],
      componentProps: {
        options: [
          {
            fullName: '工资收入',
            id: 1,
          },
          {
            fullName: '投资收益',
            id: 2,
          },
          {
            fullName: '退休金',
            id: 3,
          },
          {
            fullName: '其他',
            id: 4,
          },
        ],
      },
    },

    {
      field: 'familySupport',
      label: '6、在你的成长过程中，家庭给予你哪些方面的支持？',
      subLabel: '（简答）',
      component: 'Textarea',
      ifShow: true,
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      field: 'familyEducation',
      label: '7、你认为家庭对你的教育方式是？',
      subLabel: '（单选）',
      component: 'Radio',
      ifShow: true,
      componentProps: {
        options: [
          {
            fullName: '严格教育',
            id: 1,
          },
          {
            fullName: '宽松教育',
            id: 2,
          },
          {
            fullName: '民主教育',
            id: 3,
          },
          {
            fullName: '其他',
            id: 4,
          },
        ],
      },
    },
    {
      field: 'familyActivities',
      label: '8、你和家人经常一起参加哪些活动？',
      subLabel: '（单选）',
      component: 'Checkbox',
      ifShow: true,
      rules: [{ type: 'array' }],
      componentProps: {
        options: [
          {
            fullName: '家庭聚会',
            id: 1,
          },
          {
            fullName: '户外旅行',
            id: 2,
          },
          {
            fullName: '看电影',
            id: 3,
          },
          {
            fullName: '运动',
            id: 4,
          },
        ],
      },
    },
    {
      field: 'familyRelationship',
      label: '9、请描述你与家庭成员之间的关系（如父母、兄弟姐妹等）。',
      subLabel: '（简答）',
      component: 'Textarea',
      ifShow: true,
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      field: 'familyHealth',
      label: '10、你家庭成员的身体健康状况如何？',
      subLabel: '（单选）',
      component: 'Radio',
      ifShow: true,
      componentProps: {
        options: [
          {
            fullName: '良好',
            id: 1,
          },
          {
            fullName: '部分成员有慢性疾病',
            id: 2,
          },
          {
            fullName: '需要特别照顾',
            id: 3,
          },
          {
            fullName: '其他',
            id: 4,
          },
        ],
      },
    },
  ]);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: schemas.value,
    layout: 'vertical',
    labelWidth: '100%',
  });
  const id = ref('');
  const { createMessage } = useMessage();

  function setFormRef(el, index) {
    if (el) {
      formRefs.value[index] = el;
    }
  }
  const handleAnswer = (questionIndex, event) => {
    const currentSchema = schemas.value[questionIndex];
    currentSchema.skipQuestionIndex = 9;
    if (currentSchema.skipQuestionIndex !== undefined && event && event.length > 0) {
      // 隐藏当前题到目标题之间的所有题目
      for (let i = questionIndex + 1; i < currentSchema.skipQuestionIndex; i++) {
        schemas.value[i].ifShow = false;
      }
      // 确保目标题显示
      schemas.value[currentSchema.skipQuestionIndex].ifShow = true;
    } else {
      schemas.value.forEach(item => {
        item.ifShow = true;
      });
    }
  };

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    return values;
  }
  defineExpose({
    handleSubmit,
  });
  onMounted(() => {
    if (props.opType === 2) {
      const updates = schemas.value.map((item, index) => ({
        field: item.field,
        componentProps: {
          ...item.componentProps,
          disabled: index !== 0,
        },
      }));
      updateSchema(updates);
    }
  });
</script>
