<template>

  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content bg-white">
        <a-tabs  v-model:activeKey="activeKey" destroyInactiveTabPane>
          <a-tab-pane key="1">
            <template #tab>
              <div class="pl-10px pr-10px">国奖</div>
            </template>
            <ScholarshipList />
          </a-tab-pane>
          <a-tab-pane key="2" tab="国励">
            <EncourageScholarship />
          </a-tab-pane> <a-tab-pane key="3" tab="国助">
            <NationalStuGrant />
          </a-tab-pane> <a-tab-pane key="4" tab="义务兵">
            <compulsoryServiceman />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import ScholarshipList from './components/ScholarshipList.vue';
import NationalStuGrant from './components/NationalStuGrant.vue';
import EncourageScholarship from './components/EncourageScholarship.vue';
import compulsoryServiceman from './components/CompulsoryServiceman.vue';
const activeKey = ref('1')
</script>