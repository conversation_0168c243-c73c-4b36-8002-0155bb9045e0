<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
      <a-button preIcon="icon-ym icon-ym-btn-upload" type="primary" @click="handleImport">导入</a-button>
      <a-button preIcon="icon-ym icon-ym-btn-download" type="link" @click="handleExport">导出</a-button>
      <a-button preIcon="icon-ym icon-ym-delete" type="link" @click="handleBatchDelete">删除</a-button>
      <a-button preIcon="icon-ym icon-ym-synForThird" type="link" @click="handleSync">同步数据</a-button>
    </template>
        </BasicTable>
      </div>
    </div>
    <ImportModal @register="registerImportModal" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleDownload" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, BasicColumn, TableAction, ActionItem } from '@/components/Table';
import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
import ExportModal from '@/components/CommonModal/src/ExportModal.vue';



const { createMessage,createConfirm } = useMessage();

const columns: BasicColumn[] = [
  { title: '年度', dataIndex: 'year', width: 80, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '学号', dataIndex: 'studentNo', width: 120, resizable: true, ellipsis: true, fixed: 'left'  },
  { title: '学生姓名', dataIndex: 'studentName', width: 120, resizable: true, ellipsis: true, fixed: 'left'  },
  { title: '学校名称', dataIndex: 'schoolName', width: 120, resizable: true, ellipsis: true },
  { title: '院系名称', dataIndex: 'collegeName', width: 120, resizable: true, ellipsis: true },
  { title: '班级名称', dataIndex: 'className', width: 120, resizable: true, ellipsis: true },
  { title: '专业名称', dataIndex: 'majorName', width: 120, resizable: true, ellipsis: true },
  { title: '性别', dataIndex: 'gender', width: 60, resizable: true, ellipsis: true },
  { title: '出生年月', dataIndex: 'birthday', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '身份证号', dataIndex: 'idCard', width: 150, resizable: true, ellipsis: true },
  { title: '政治面貌', dataIndex: 'political', width: 120, resizable: true, ellipsis: true },
  { title: '民族', dataIndex: 'nation', width: 80, resizable: true, ellipsis: true },
  { title: '入学时间', dataIndex: 'admission', width: 100, resizable: true, ellipsis: true },
  { title: '学制', dataIndex: 'schooling', width: 80, resizable: true, ellipsis: true },
  { title: '联系电话', dataIndex: 'phone', width: 120, resizable: true, ellipsis: true },
  { title: '家庭户口', dataIndex: 'familyRegister', width: 120, resizable: true, ellipsis: true },
  { title: '家庭人口数', dataIndex: 'familyCount', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '家庭月收入', dataIndex: 'familyIncome', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '人均月收入', dataIndex: 'perIncome', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '收入来源', dataIndex: 'incomeSource', width: 120, resizable: true, ellipsis: true },
  { title: '家庭邮编', dataIndex: 'familyPostcode', width: 100, resizable: true, ellipsis: true },
  { title: '家庭地址', dataIndex: 'familyAddress', width: 150, resizable: true, ellipsis: true },
  { title: '必修课程数', dataIndex: 'requiredCourseCount', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '及格课程数', dataIndex: 'passCourseCount', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '成绩排名名次', dataIndex: 'rank', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '成绩排名总人数', dataIndex: 'rankTotal', width: 120, resizable: true, ellipsis: true, align: 'right' },
  { title: '行政职务', dataIndex: 'adminJob', width: 100, resizable: true, ellipsis: true },
  { title: '实行综合排名', dataIndex: 'compositeRank', width: 120, resizable: true, ellipsis: true },
  { title: '申请理由', dataIndex: 'applyReason', width: 120, resizable: true, ellipsis: true },
  { title: '申请日期', dataIndex: 'applyDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '推荐理由', dataIndex: 'recommendReason', width: 120, resizable: true, ellipsis: true },
  { title: '推荐日期', dataIndex: 'recommendDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '院系意见', dataIndex: 'collegeOpinion', width: 120, resizable: true, ellipsis: true },
  { title: '院系审核日期', dataIndex: 'collegeAuditDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '院系评审人', dataIndex: 'collegeReviewer', width: 120, resizable: true, ellipsis: true },
  { title: '学校意见', dataIndex: 'schoolOpinion', width: 120, resizable: true, ellipsis: true },
  { title: '学校日期', dataIndex: 'schoolDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '审核状态', dataIndex: 'auditStatus', width: 100, resizable: true, ellipsis: true },
  { title: '是否同步', dataIndex: 'isSync', width: 80, resizable: true, ellipsis: true },
  { title: '失败原因', dataIndex: 'failReason', width: 120, resizable: true, ellipsis: true },
  { title: '同步描述', dataIndex: 'syncDesc', width: 120, resizable: true, ellipsis: true },
];


const formConfig = {
  schemas: [
    {
      field: 'keyword',
      label: '',
      component: 'Input',
      componentProps: {
        placeholder: '请输入学生姓名/学号',
      },
    },
  ],
};

const [registerTable, { reload, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  dataSource: [
    {
      id: 1,
      year: '2023',
      studentNo: '20230001',
      studentName: '李里',
      schoolName: '江苏大学',
      collegeName: '计算机学院',
      className: '计科1班',
      majorName: '计算机科学与技术',
      gender: '男',
      birthday: '2002-01-01',
      idCard: '320000200201010011',
      political: '共青团员',
      nation: '汉族',
      admission: '2020-09',
      schooling: '四年',
      phone: '13800000000',
      familyRegister: '江苏南京',
      familyCount: 4,
      familyIncome: 8000,
      perIncome: 2000,
      incomeSource: '工资',
      familyPostcode: '210000',
      familyAddress: '南京市鼓楼区xx路',
      requiredCourseCount: 20,
      passCourseCount: 19,
      rank: 3,
      rankTotal: 50,
      compositeRank: '是',
      adminJob: '班长',
      applyReason: '家庭经济困难',
      applyDate: '2023-09-01',
      recommendReason: '表现优秀',
      recommendDate: '2023-09-05',
      collegeOpinion: '同意',
      collegeAuditDate: '2023-09-10',
      collegeReviewer: '李老师',
      schoolOpinion: '同意',
      schoolDate: '2023-09-15',
      auditStatus: '已审核',
      isSync: '是',
      failReason: '',
      syncDesc: '',
    },
  ],
  columns,
  useSearchForm: true,
  formConfig,
  showIndexColumn: true,
  rowSelection: { type: 'checkbox' },
  rowKey: 'id',
});

const [registerImportModal, { openModal: openImportModal }] = useModal();
const [registerExportModal, { openModal: openExportModal }] = useModal();

function handleImport() {
  openImportModal(true, { url: '/api/national/import' });
}
function handleExport() {
  openExportModal(true, { columnList:columns });
}
function handleDownload(data) {
  createMessage.success('导出成功');
}
function handleBatchDelete() {
  const ids = getSelectRowKeys();
  if (!ids.length) return createMessage.error('请选择要删除的数据');
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '删除后不能撤销，确定要删除吗?',
    onOk: () => {
      createMessage.success('删除成功');
      clearSelectedRowKeys();
      reload();
    },
  });
}
function handleSync() {
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '将对所有的数据进行同步操作，是否确定?',
    onOk: () => {
      createMessage.success('同步成功');
      reload();
    },
  });
 
}
</script>
