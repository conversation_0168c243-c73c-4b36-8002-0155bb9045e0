<!--
 * @Description: 国奖
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 11:59:07
-->
<template>
  <BasicTable @register="registerTable">
    <!-- 左侧按钮组 -->
    <template #tableTitle>
      <a-button preIcon="icon-ym icon-ym-btn-upload" type="primary" @click="handleImport">导入</a-button>
      <a-button preIcon="icon-ym icon-ym-btn-download" type="link" @click="handleExport">导出</a-button>
      <a-button preIcon="icon-ym icon-ym-delete" type="link" @click="handleBatchDelete">删除</a-button>
      <a-button preIcon="icon-ym icon-ym-synForThird" type="link" @click="handleSync">同步数据</a-button>
    </template>
  </BasicTable>
  <!-- 导入弹窗 -->
  <ImportModal @register="registerImportModal" @reload="reload" />
  <!-- 导出弹窗 -->
  <ExportModal @register="registerExportModal" @download="handleDownload" />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, BasicColumn, TableAction, ActionItem } from '@/components/Table';
import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
import ExportModal from '@/components/CommonModal/src/ExportModal.vue';

const { createMessage, createConfirm } = useMessage();

// 表格列配置
const columns: BasicColumn[] = [
  { title: '年度', dataIndex: 'year', width: 80, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '学生姓名', dataIndex: 'studentName', width: 120, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '学号', dataIndex: 'studentNo', width: 120, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '学校名称', dataIndex: 'schoolName', width: 120, resizable: true, ellipsis: true },
  { title: '院系名称', dataIndex: 'collegeName', width: 120, resizable: true, ellipsis: true },
  { title: '专业名称', dataIndex: 'majorName', width: 120, resizable: true, ellipsis: true },
  { title: '班级名称', dataIndex: 'className', width: 120, resizable: true, ellipsis: true },
  { title: '出生年月', dataIndex: 'birthday', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '性别', dataIndex: 'gender', width: 60, resizable: true, ellipsis: true },
  { title: '身份证号', dataIndex: 'idCard', width: 150, resizable: true, ellipsis: true },
  { title: '政治面貌', dataIndex: 'political', width: 120, resizable: true, ellipsis: true },
  { title: '入学时间', dataIndex: 'admission', width: 100, resizable: true, ellipsis: true },
  { title: '民族', dataIndex: 'nation', width: 80, resizable: true, ellipsis: true },
  { title: '学制', dataIndex: 'schooling', width: 80, resizable: true, ellipsis: true },
  { title: '联系电话', dataIndex: 'phone', width: 120, resizable: true, ellipsis: true },
  { title: '必修课程数', dataIndex: 'requiredCourseCount', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '及格课程数', dataIndex: 'passCourseCount', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '成绩排名名次', dataIndex: 'rank', width: 100, resizable: true, ellipsis: true, align: 'right' },
  { title: '成绩排名总人数', dataIndex: 'rankTotal', width: 120, resizable: true, ellipsis: true, align: 'right' },
  { title: '实行综合排名', dataIndex: 'compositeRank', width: 120, resizable: true, ellipsis: true },
  { title: '申请理由', dataIndex: 'applyReason', width: 120, resizable: true, ellipsis: true },
  { title: '申请日期', dataIndex: 'applyDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '推荐理由', dataIndex: 'recommendReason', width: 120, resizable: true, ellipsis: true },
  { title: '推荐日期', dataIndex: 'recommendDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '院系意见', dataIndex: 'collegeOpinion', width: 120, resizable: true, ellipsis: true },
  { title: '填报日期', dataIndex: 'reportDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '学校意见', dataIndex: 'schoolOpinion', width: 120, resizable: true, ellipsis: true },
  { title: '学校日期', dataIndex: 'schoolDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '审核状态', dataIndex: 'auditStatus', width: 100, resizable: true, ellipsis: true },
  { title: '失败原因', dataIndex: 'failReason', width: 120, resizable: true, ellipsis: true },
  { title: '同步描述', dataIndex: 'syncDesc', width: 120, resizable: true, ellipsis: true },
  { title: '是否同步', dataIndex: 'isSync', width: 80, resizable: true, ellipsis: true },
];

// 操作列配置
function getTableActions(record): ActionItem[] {
  return [
    {
      label: '删除',
      onClick: () => handleDelete(record),
    },
    {
      label: '同步数据',
      onClick: () => handleSync(record),
    },
  ];
}

// 搜索表单配置
const formConfig = {
  schemas: [
    {
      field: 'keyword',
      label: '',
      component: 'Input',
      componentProps: {
        placeholder: '请输入学生姓名/学号',
      },
    },
  ],
};

// 表格注册
const [registerTable, { reload, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  dataSource: [
    {
      id: 1,
      year: '2017',
      studentName: '张三',
      studentNo: '17220215',
      schoolName: '学号不存在',
      collegeName: '',
      majorName: '英语师范',
      className: '15英语师范3班',
      birthday: '1997-05-16',
      gender: '女',
      idCard: '3210881997...',
      political: '中共预备党员',
      admission: '2015-9',
      nation: '汉族',
      schooling: '四年',
      phone: '13800000000',
      requiredCourseCount: 20,
      passCourseCount: 19,
      rank: 3,
      rankTotal: 50,
      compositeRank: '是',
      applyReason: '家庭困难',
      applyDate: '2023-09-01',
      recommendReason: '表现优秀',
      recommendDate: '2023-09-05',
      collegeOpinion: '同意',
      reportDate: '2023-09-10',
      schoolOpinion: '同意',
      schoolDate: '2023-09-15',
      auditStatus: '已审核',
      failReason: '',
      syncDesc: '',
      isSync: '是',
    },
    {
      id: 2,
      year: '2017',
      studentName: '张忠',
      studentNo: '2017000010',
      schoolName: '重复数据',
      collegeName: '',
      majorName: '网络与新媒体',
      className: '15网络与新媒体3班',
      birthday: '1997-06-21',
      gender: '女',
      idCard: '3206211997...',
      political: '中共预备党员',
      admission: '2015-9',
      nation: '汉族',
      schooling: '四年',
      phone: '13800000000',
      requiredCourseCount: 20,
      passCourseCount: 19,
      rank: 3,
      rankTotal: 50,
      compositeRank: '是',
      applyReason: '家庭困难',
      applyDate: '2023-09-01',
      recommendReason: '表现优秀',
      recommendDate: '2023-09-05',
      collegeOpinion: '同意',
      reportDate: '2023-09-10',
      schoolOpinion: '同意',
      schoolDate: '2023-09-15',
      auditStatus: '已审核',
      failReason: '',
      syncDesc: '',
      isSync: '是',
    },
  ],
  columns,
  useSearchForm: true,
  formConfig,
  showIndexColumn: true,
  rowKey: 'id',
  rowSelection: 'checkbox',
});

// 导入/导出弹窗注册
const [registerImportModal, { openModal: openImportModal }] = useModal();
const [registerExportModal, { openModal: openExportModal }] = useModal();

function handleImport() {
  openImportModal(true, { url: '/api/scholarship/import' });
}
function handleExport() {
  openExportModal(true, { columnList:columns });
}
function handleDownload(data) {
  // 实际下载逻辑
  createMessage.success('导出成功');
}
function handleBatchDelete() {
  const ids = getSelectRowKeys();
  if (!ids.length) return createMessage.error('请选择要删除的数据');
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '删除后不能撤销，确定要删除吗?',
    onOk: () => {
      createMessage.success('删除成功');
      clearSelectedRowKeys();
      reload();
    },
  });
}
function handleSync() {
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '将对所有的数据进行同步操作，是否确定?',
    onOk: () => {
      createMessage.success('同步成功');
      reload();
    },
  });
 
}
</script>