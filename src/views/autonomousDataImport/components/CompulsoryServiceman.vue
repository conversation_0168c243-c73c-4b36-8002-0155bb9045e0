<!--
 * @Description: 义务兵
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-24 11:59:07
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button preIcon="icon-ym icon-ym-btn-upload" type="primary" @click="handleImport">导入</a-button>
            <a-button preIcon="icon-ym icon-ym-btn-download" type="link" @click="handleExport">导出</a-button>
            <a-button preIcon="icon-ym icon-ym-delete" type="link" @click="handleBatchDelete">删除</a-button>
            <a-button preIcon="icon-ym icon-ym-synForThird" type="link" @click="handleSync">同步数据</a-button>
          </template>
        </BasicTable>
      </div>
    </div>
    <ImportModal @register="registerImportModal" @reload="reload" />
    <ExportModal @register="registerExportModal" @download="handleDownload" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useMessage } from '@/hooks/web/useMessage';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, BasicColumn, TableAction, ActionItem } from '@/components/Table';
import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
import ExportModal from '@/components/CommonModal/src/ExportModal.vue';


const { createMessage, createConfirm } = useMessage();

const columns: BasicColumn[] = [
  { title: '年度', dataIndex: 'year', width: 80, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '学号', dataIndex: 'studentNo', width: 120, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '姓名', dataIndex: 'soldierName', width: 80, resizable: true, ellipsis: true, fixed: 'left' },
  { title: '性别', dataIndex: 'gender', width: 60, resizable: true, ellipsis: true },
  { title: '出生年月', dataIndex: 'birthday', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '入伍时间', dataIndex: 'enlistDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '退伍时间', dataIndex: 'dischargeDate', width: 120, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD' },
  { title: '服役部队', dataIndex: 'armyName', width: 150, resizable: true, ellipsis: true },
  { title: '服役地区', dataIndex: 'armyArea', width: 120, resizable: true, ellipsis: true },
  { title: '政治面貌', dataIndex: 'political', width: 120, resizable: true, ellipsis: true },
  { title: '民族', dataIndex: 'nation', width: 80, resizable: true, ellipsis: true },
  { title: '联系电话', dataIndex: 'phone', width: 120, resizable: true, ellipsis: true },
  { title: '家庭住址', dataIndex: 'address', width: 150, resizable: true, ellipsis: true },
  { title: '退伍证编号', dataIndex: 'dischargeNo', width: 150, resizable: true, ellipsis: true },
  { title: '审核状态', dataIndex: 'auditStatus', width: 100, resizable: true, ellipsis: true },
  { title: '是否同步', dataIndex: 'isSync', width: 80, resizable: true, ellipsis: true },
  { title: '失败原因', dataIndex: 'failReason', width: 120, resizable: true, ellipsis: true },
  { title: '同步描述', dataIndex: 'syncDesc', width: 120, resizable: true, ellipsis: true },
];

const formConfig = {
  schemas: [
    {
      field: 'keyword',
      label: '',
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名/学号',
      },
    },
  ],
};

const [registerTable, { reload, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
  columns,
  useSearchForm: true,
  formConfig,
  showIndexColumn: true,
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
  },
  dataSource: [
    {
      id: 1,
      year: '2022',
      studentNo: '20220001',
      soldierName: '王强',
      gender: '男',
      birthday: '2001-03-15',
      enlistDate: '2022-09-01',
      dischargeDate: '2024-09-01',
      armyName: '陆军第1集团军',
      armyArea: '江苏南京',
      political: '共青团员',
      nation: '汉族',
      phone: '13811112222',
      address: '南京市鼓楼区xx路',
      dischargeNo: '320000202409010011',
      auditStatus: '已审核',
      isSync: '是',
      failReason: '',
      syncDesc: '',
    },
    {
      id: 2,
      year: '2021',
      studentNo: '20210002',
      soldierName: '李明',
      gender: '男',
      birthday: '2000-07-20',
      enlistDate: '2021-09-01',
      dischargeDate: '2023-09-01',
      armyName: '海军东海舰队',
      armyArea: '上海',
      political: '群众',
      nation: '汉族',
      phone: '13822223333',
      address: '上海市浦东新区xx路',
      dischargeNo: '310000202309010022',
      auditStatus: '已审核',
      isSync: '是',
      failReason: '',
      syncDesc: '',
    },
    {
      id: 3,
      year: '2023',
      studentNo: '20230003',
      soldierName: '张伟',
      gender: '男',
      birthday: '2002-11-05',
      enlistDate: '2023-09-01',
      dischargeDate: '',
      armyName: '火箭军某部',
      armyArea: '北京',
      political: '共青团员',
      nation: '汉族',
      phone: '13833334444',
      address: '北京市海淀区xx路',
      dischargeNo: '',
      auditStatus: '审核中',
      isSync: '否',
      failReason: '',
      syncDesc: '',
    },
    {
      id: 4,
      year: '2020',
      studentNo: '20200004',
      soldierName: '赵磊',
      gender: '男',
      birthday: '1999-05-10',
      enlistDate: '2020-09-01',
      dischargeDate: '2022-09-01',
      armyName: '空军某部',
      armyArea: '四川成都',
      political: '群众',
      nation: '藏族',
      phone: '13844445555',
      address: '成都市武侯区xx路',
      dischargeNo: '510000202209010033',
      auditStatus: '已审核',
      isSync: '是',
      failReason: '',
      syncDesc: '',
    },
    {
      id: 5,
      year: '2022',
      studentNo: '20220005',
      soldierName: '刘洋',
      gender: '女',
      birthday: '2001-12-25',
      enlistDate: '2022-09-01',
      dischargeDate: '',
      armyName: '陆军第2集团军',
      armyArea: '江苏苏州',
      political: '共青团员',
      nation: '汉族',
      phone: '13855556666',
      address: '苏州市姑苏区xx路',
      dischargeNo: '',
      auditStatus: '审核中',
      isSync: '否',
      failReason: '',
      syncDesc: '',
    },
  ],
});

const [registerImportModal, { openModal: openImportModal }] = useModal();
const [registerExportModal, { openModal: openExportModal }] = useModal();

function handleImport() {
  openImportModal(true, { url: '/api/soldier/import' });
}
function handleExport() {
  openExportModal(true, { columnList:columns });
}
function handleDownload(data) {
  createMessage.success('导出成功');
}
function handleBatchDelete() {
  const ids = getSelectRowKeys();
  if (!ids.length) return createMessage.error('请选择要删除的数据');
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '删除后不能撤销，确定要删除吗?',
    onOk: () => {
      createMessage.success('删除成功');
      clearSelectedRowKeys();
      reload();
    },
  });
}
function handleSync() {
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '将对所有的数据进行同步操作，是否确定?',
    onOk: () => {
      createMessage.success('同步成功');
      reload();
    },
  });
 
}
</script>