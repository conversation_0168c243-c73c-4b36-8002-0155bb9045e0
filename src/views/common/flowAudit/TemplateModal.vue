<template>
  <Drawer class="drawer-box" v-model:open="props.visible" title="更多版本" placement="right" @close="handleClose">
    <div class="template-box row">
      <div
        class="template-item"
        v-for="item in data"
        :key="item.id"
        :class="{ active: item.state == 1, inactive: item.state == 0, selected: selectedId === item.id }"
        @click="handleSelect(item)">
        <div>{{ item.fullName }}</div>
        <a-switch v-model:checked="item.isEnable" @change="getCheckedTemplate(item)"></a-switch>
      </div>
    </div>
    <template #footer>
      <a-button class="ml-10px" @click="handleClose">取消</a-button>
    </template>
  </Drawer>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { Drawer, Button, Spin } from 'ant-design-vue';
  import { ScrollContainer } from '@/components/Container';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getVersionList, getFlowInfo, saveFlow } from '@/api/workFlow/template';
  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: [],
    },
  });
  const { createMessage, createConfirm } = useMessage();
  function handleClose() {
    emit('handleOk', false);
  }
  const selectedId = ref('');
  const emit = defineEmits('selected');
  function handleSelect(item) {
    selectedId.value = item.id;
    emit('selected', item);
  }

  async function getCheckedTemplate(item) {
    const { data } = await getFlowInfo(item.id);
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: `是否确认切换到${item.fullName}?`,
      onOk: async () => {
        handleEnableTemplate(data);
      },
      onCancel: async () => {
        emit('handleOk', false);
      },
    });
  }
  function handleEnableTemplate(data) {
    const query = {
      flowConfig: data.flowConfig,
      flowId: data.flowId,
      flowNodes: data.flowNodes,
      flowXml: data.flowXml,
      id: data.id,
      type: 1,
    };
    saveFlow(query).then(res => {
      createMessage.success(res.msg);
      // fetchTemplate();
      emit('handleOk', false);
    });
  }
</script>
<style lang="less" scoped>
  .template-box {
    gap: 6px;
    width: 100%;
    margin: 0 auto;
    flex-wrap: wrap;
    align-items: center;
    .template-item {
      position: relative;
      transition: border-width 0.1s ease; /* 添加过渡效果 */
      padding: 6px 10px;
      overflow: hidden;
      border: #dddddd 1px solid;
      margin: 10px;
      cursor: pointer;
      border-radius: 4px;
      div {
        margin-bottom: 6px;
      }
    }
    .active {
      border-color: @primary-color;
      &::after {
        content: '已启用';
        top: 10px;
        right: -20px;
        width: 80px;
        height: 20px;
        line-height: 20px;
        position: absolute;
        transform: rotate(45deg);
        font-size: 12px;
        text-align: center;
        color: #fff;
        background: @primary-color;
      }
    }
    .selected {
      border: 2px solid @primary-color;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    }
    .inactive {
      border-color: #f5a12f;
      &::after {
        content: '未发布';
        top: 10px;
        right: -20px;
        width: 80px;
        height: 20px;
        line-height: 20px;
        position: absolute;
        transform: rotate(45deg);
        font-size: 12px;
        text-align: center;
        color: #fff;
        background: #f5a12f;
      }
    }
  }
  .row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
</style>
