<template>
  <div class="mtcn-content-wrapper">
    <div class="content-box">
      <div class="template-box row">
        <div
          class="template-item"
          v-for="item in templateData.slice(0, props.maxCount)"
          :key="item.id"
          :class="{ active: item.state == 1, inactive: item.state == 0, selected: selectedId === item.id }"
          @click="fetchProcess(item.id)">
          <div>{{ item.fullName }}</div>
          <a-switch v-model:checked="item.isEnable" @change="getCheckedTemplate(item)"></a-switch>
        </div>
        <a-button
          @click="handleSelectMoreTemplate"
          v-if="templateData.length > props.maxCount"
          type="primary"
          preIcon="icon-ym icon-ym-app-more"
          style="height: 62px"
          >更多版本</a-button
        >
      </div>
      <Spin :spinning="spinning">
        <div :class="['flow-node', { 'flow-node-row': props.layOut === 'horizontal' }]" v-if="flowInfo?.nodes?.length > 0">
          <template v-for="item in flowInfo.nodes" :key="item.type">
            <template v-if="item.type != 'connect'">
              <div class="flow-btn start row" v-if="item.type === 'start'">
                <i class="icon-ym icon-ym-flow-node-start start-icon"></i>
                <div>{{ item.nodeName }}</div>
              </div>
              <div class="flow-btn start row" v-else-if="item.type === 'end'">
                <i class="icon-ym icon-ym-flow-node-end end-icon"></i>
                <div>{{ item.nodeName }}</div>
              </div>
              <div class="flow-box" v-else>
                <div class="flow-header">
                  <i class="icon-ym icon-ym-user"></i>
                  {{ item.nodeName }}
                </div>
                <div class="flow-content">{{ item.content }}</div>
              </div>
              <div v-if="item.type !== 'end'" :class="['line-y', { 'line-x': props.layOut === 'horizontal' }]"></div>
            </template>
          </template>
        </div>
        <Empty :image="simpleImage" v-else />
      </Spin>
    </div>
  </div>

  <TemplateModal :visible="templateVisible" :data="templateData.slice(props.maxCount)" @selected="handleSelected" @handleOk="handleOk" />
</template>
<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useModal } from '@/components/Modal';
  import { Spin, Empty, Descriptions as ADescriptions, DescriptionsItem as ADescriptionsItem } from 'ant-design-vue';

  import { useBaseStore } from '@/store/modules/base';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import TemplateModal from './TemplateModal.vue';
  import { getVersionList, getFlowInfo, saveFlow } from '@/api/workFlow/template';
  import { getPermissionSelector } from '@/api/permission/permissionGroup';
  const { t } = useI18n();
  const simpleImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);
  const props = defineProps({
    templatedId: String,
    maxCount: {
      type: Number,
      default: 2, //默认最大展示数量
    },
    layOut: {
      type: String,
      default: 'vertical', //流程节点横向/纵向布局，默认纵向布局。horizontal：横向布局；vertical：纵向布局。
    },
  });
  const flowList = ref([]);
  const { createMessage, createConfirm } = useMessage();
  const baseStore = useBaseStore();
  const schemas: FormSchema[] = ref([
    {
      field: 'shlc',
      label: '审核流程',
      component: 'Input',
      slot: 'shlc',
    },
  ]);
  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields, updateSchema }] = useForm({ schemas: schemas, labelWidth: '135px' });
  const templateVisible = ref(false);
  function handleSelectMoreTemplate() {
    templateVisible.value = !templateVisible.value;
  }

  const templateData = ref([]);
  const flowInfo = ref({});
  const activeTemplate = ref('');
  async function fetchTemplate() {
    if (!props.templatedId) return;
    const { data } = await getVersionList(props.templatedId);
    templateData.value = data;
    templateData.value.forEach(item => {
      item.isEnable = item.state == 1;
    });
    await fetchProcess(templateData.value[0].id);
    activeTemplate.value = templateData.value[0].id;
  }
  const spinning = ref(false);
  const selectedId = ref('');

  async function fetchProcess(id) {
    spinning.value = true;
    selectedId.value = id;
    templateVisible.value = false;
    const { data } = await getFlowInfo(id);
    activeTemplate.value = id;
    const nodes = Object.values(data.flowNodes).filter(node => node.type !== 'global');

    // 排序逻辑
    nodes.sort((a, b) => {
      if (a.type === 'start') return -1; // 流程发起在最前面
      if (b.type === 'start') return 1;

      if (a.type === 'end') return 1; // 流程结束在最后面
      if (b.type === 'end') return -1;

      // 其他节点按 managerLevel 升序排序
      return a.managerLevel - b.managerLevel;
    });
    flowInfo.value = { ...data, nodes };
    spinning.value = false;
  }
  function handleSelected(item) {
    templateData.value = templateData.value.filter(e => item.id != e.id);
    templateData.value.unshift(item);
    fetchProcess(item.id);
  }
  function getCheckedTemplate(item) {
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: `是否确认切换到${item.fullName}?`,
      onOk: async () => {
        await fetchProcess(item.id);
        handleEnableTemplate();
      },
      onCancel: async () => {
        await fetchTemplate();
      },
    });
  }
  function handleEnableTemplate() {
    const query = {
      flowConfig: flowInfo.value.flowConfig,
      flowId: flowInfo.value.flowId,
      flowNodes: flowInfo.value.flowNodes,
      flowXml: flowInfo.value.flowXml,
      id: flowInfo.value.id,
      type: 1,
    };
    saveFlow(query).then(res => {
      createMessage.success(res.msg);
      fetchTemplate();
    });
  }
  function handleOk() {
    templateVisible.value = false;
    fetchTemplate();
  }
  onMounted(() => {
    fetchTemplate();
  });
</script>
<style lang="less" scoped>
  .content-box {
    width: 90%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .template-box {
      gap: 6px;
      width: 100%;
      margin: 0 auto;
      // flex-wrap: wrap;
      align-items: center;
      .template-item {
        position: relative;
        width: 170px;
        transition: border-width 0.1s ease; /* 添加过渡效果 */
        // height: 50px;
        // line-height: 50px;
        // text-align: center;
        padding: 6px 10px;
        overflow: hidden;
        border: #dddddd 1px solid;
        margin: 10px;
        cursor: pointer;
        border-radius: 4px;
        div {
          margin-bottom: 6px;
        }
      }
      .active {
        border-color: @primary-color;
        &::after {
          content: '已启用';
          top: 10px;
          right: -20px;
          width: 80px;
          height: 20px;
          line-height: 20px;
          position: absolute;
          transform: rotate(45deg);
          font-size: 12px;
          text-align: center;
          color: #fff;
          background: @primary-color;
        }
      }
      .selected {
        border: 2px solid @primary-color;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
      }
      .inactive {
        border-color: #f5a12f;
        &::after {
          content: '未发布';
          top: 10px;
          right: -20px;
          width: 80px;
          height: 20px;
          line-height: 20px;
          position: absolute;
          transform: rotate(45deg);
          font-size: 12px;
          text-align: center;
          color: #fff;
          background: #f5a12f;
        }
      }
    }
    .row {
      display: flex;
      gap: 4px;
      justify-content: center;
      align-items: center;
      flex-direction: row;
    }
    .flow-node-row {
      flex-direction: row !important;
      // flex-wrap: wrap;
    }
    .flow-node {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .start {
        width: 130px;
        padding: 10px 20px;
        border: #dddddd 1px solid;
        margin: 10px 0;
        border-radius: 24px;
        .start-icon {
          color: @primary-color;
        }
        .end-icon {
          color: #888888;
        }
        i {
          font-size: 18px;
        }
      }
      .flow-col {
        display: flex;
        align-items: center;
        flex-direction: column;
      }
      .line-x {
        width: 20px !important;
        height: 2px !important;
        background: #dddddd;
        display: inline-block;
      }
      .line-y {
        width: 2px;
        height: 20px;
        background: #dddddd;
        display: inline-block;
      }
      .flow-box {
        position: relative;
        overflow: hidden;
        width: 260px;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: baseline;
        gap: 4px;
        border: #dddddd 1px solid;
        margin: 10px 0;
        cursor: pointer;
        border-radius: 6px;
        .flow-header {
          width: 100%;
          background: #1890ff;
          color: #ffffff;
          padding: 6px;
        }
        .flow-content {
          padding: 6px;
          width: 100%;
          overflow-wrap: break-word;
        }
        .dis-check {
          color: #00000040 !important;
        }
      }
    }
  }
</style>
